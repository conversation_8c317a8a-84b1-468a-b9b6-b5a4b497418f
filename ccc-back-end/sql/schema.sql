-- This script should be managed by a migration tool like Alembic.

-- Enable UUID extension for unique identifiers
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Stores information about the career coaches (users of the platform)
CREATE TABLE coaches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    firebase_uid VARCHAR(128) UNIQUE NOT NULL, -- The unique identifier from Firebase Authentication
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_login_at TIMESTAMPTZ,
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Links coaches to their clients, whose full profiles are in Cosmos DB.
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    coach_id UUID NOT NULL REFERENCES coaches(id) ON DELETE CASCADE,
    cosmos_user_id UUID UNIQUE NOT NULL, -- The 'userId' from the Cosmos DB profile JSON
    cosmos_profile_id UUID UNIQUE NOT NULL, -- The 'id' (profile id) from the Cosmos DB profile JSON
    added_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    client_full_name VARCHAR(255), -- Denormalized for quick display on dashboards
    -- A coach cannot have the same client listed more than once.
    UNIQUE(coach_id, cosmos_user_id)
);

-- Stores curated job market data for the agents to query.
CREATE TABLE job_market_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trend_title VARCHAR(255) NOT NULL,
    summary TEXT NOT NULL,
    source_url VARCHAR(512),
    data_type VARCHAR(50) NOT NULL CHECK (data_type IN ('TREND', 'SALARY_INFO', 'SKILL_DEMAND')),
    region VARCHAR(100) NOT NULL DEFAULT 'Global',
    industry VARCHAR(100),
    recorded_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Stores the final generated career reports.
CREATE TABLE reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    coach_id UUID NOT NULL REFERENCES coaches(id) ON DELETE CASCADE,
    report_content TEXT NOT NULL, -- The full, rendered HTML content of the report
    access_token VARCHAR(64) UNIQUE NOT NULL DEFAULT REPLACE(uuid_generate_v4()::TEXT, '-', ''),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    sent_at TIMESTAMPTZ, -- Tracks if/when the report was sent to the client
    -- The session ID from which this report was generated, for traceability.
    source_session_id VARCHAR(255)
);

-- Create indexes for query performance optimization.
CREATE INDEX idx_clients_coach_id ON clients(coach_id);
CREATE INDEX idx_reports_client_id ON reports(client_id);
CREATE INDEX idx_reports_coach_id ON reports(coach_id);
CREATE INDEX idx_reports_access_token ON reports(access_token);