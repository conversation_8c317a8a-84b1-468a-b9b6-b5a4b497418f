# Dashboard API Documentation

## Overview

The Dashboard API provides endpoints for career coaches to access summary information, manage clients, browse talent pools, and view job listings. All endpoints require authentication via JW<PERSON> token.

**Base URL**: `/dashboard`

**Authentication**: All endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

---

## Endpoints

### 1. Get Dashboard Summary

**Endpoint**: `GET /dashboard/summary`

**Description**: Retrieves summary statistics for the coach's dashboard including client count, talent pool size, active jobs, and agent status.

**Authentication**: Required (Coach)

**Parameters**: None

**Response Model**: `DashboardSummary`

**Response Schema**:
```json
{
  "my_clients_count": 0,
  "global_talent_pool_count": 0,
  "active_jobs_count": 0,
  "active_agents": [
    {
      "name": "string",
      "status": "string",
      "sub_agent": [
        {
          "name": "string",
          "status": "string"
        }
      ]
    }
  ]
}
```

**Response Fields**:
- `my_clients_count` (integer): Number of clients assigned to the current coach
- `global_talent_pool_count` (integer): Total number of profiles in the talent pool
- `active_jobs_count` (integer): Number of active job listings
- `active_agents` (array): List of active agents and their sub-agents

**Example Response**:
```json
{
  "my_clients_count": 15,
  "global_talent_pool_count": 1250,
  "active_jobs_count": 89,
  "active_agents": [
    {
      "name": "Career Coach Agent",
      "status": "active",
      "sub_agent": [
        {
          "name": "Talent Agent",
          "status": "active"
        },
        {
          "name": "Recruiter Agent",
          "status": "active"
        }
      ]
    }
  ]
}
```

**Error Responses**:
- `401 Unauthorized`: Invalid or missing authentication token
- `500 Internal Server Error`: Server error occurred

---

### 2. Get My Clients

**Endpoint**: `GET /dashboard/my-clients`

**Description**: Retrieves the coach's clients with their detailed profile information from both PostgreSQL and Cosmos DB.

**Authentication**: Required (Coach)

**Query Parameters**:
- `skip` (integer, optional): Number of records to skip for pagination (default: 0)
- `limit` (integer, optional): Maximum number of records to return (default: 20)

**Response Model**: `list[dict]`

**Response Schema**:
```json
[
  {
    "client_id": "string",
    "cosmos_user_id": "string",
    "cosmos_profile_id": "string",
    "client_full_name": "string",
    "added_at": "string (ISO datetime)",
    "profile": {
      // Cosmos DB profile object or null
    }
  }
]
```

**Response Fields**:
- `client_id` (string): Unique client identifier in PostgreSQL
- `cosmos_user_id` (string): User identifier in Cosmos DB
- `cosmos_profile_id` (string): Profile identifier in Cosmos DB
- `client_full_name` (string): Client's full name
- `added_at` (string): ISO datetime when client was added to coach
- `profile` (object|null): Complete profile data from Cosmos DB, null if not found

**Example Request**:
```
GET /dashboard/my-clients?skip=0&limit=10
```

**Example Response**:
```json
[
  {
    "client_id": "123e4567-e89b-12d3-a456-426614174000",
    "cosmos_user_id": "456e7890-e89b-12d3-a456-426614174001",
    "cosmos_profile_id": "789e0123-e89b-12d3-a456-426614174002",
    "client_full_name": "John Doe",
    "added_at": "2024-01-15T10:30:00Z",
    "profile": {
      "id": "789e0123-e89b-12d3-a456-426614174002",
      "full_name": "John Doe",
      "email": "<EMAIL>",
      "skills": ["Python", "Data Analysis"],
      "experience_level": "Mid-level",
      "job_search_status": "actively_looking"
    }
  }
]
```

**Error Responses**:
- `401 Unauthorized`: Invalid or missing authentication token
- `500 Internal Server Error`: Server error occurred

**Notes**:
- If a profile is not found in Cosmos DB, the `profile` field will be `null` but the client record is still returned
- Pagination is supported through `skip` and `limit` parameters

---

### 3. Get Talent Pool

**Endpoint**: `GET /dashboard/talent-pool`

**Description**: Retrieves talent pool data with optional filtering by job search status and preferred industries.

**Authentication**: Required (Coach)

**Query Parameters**:
- `skip` (integer, optional): Number of records to skip for pagination (default: 0)
- `limit` (integer, optional): Maximum number of records to return (default: 20)
- `job_search_status` (string, optional): Filter by job search status
- `preferred_industries` (array[string], optional): Filter by preferred industries

**Response Model**: `list[dict]`

**Example Request**:
```
GET /dashboard/talent-pool?skip=0&limit=10&job_search_status=actively_looking&preferred_industries=Technology&preferred_industries=Finance
```

**Response Schema**:
```json
[
  {
    // Profile objects from Cosmos DB
    "id": "string",
    "full_name": "string",
    "email": "string",
    "job_search_status": "string",
    "preferred_industries": ["string"],
    // ... other profile fields
  }
]
```

**Common Job Search Status Values**:
- `actively_looking`
- `passively_looking`
- `not_looking`
- `employed`

**Example Response**:
```json
[
  {
    "id": "profile-123",
    "full_name": "Jane Smith",
    "email": "<EMAIL>",
    "job_search_status": "actively_looking",
    "preferred_industries": ["Technology", "Fintech"],
    "skills": ["JavaScript", "React", "Node.js"],
    "experience_level": "Senior",
    "location": "San Francisco, CA"
  }
]
```

**Error Responses**:
- `401 Unauthorized`: Invalid or missing authentication token
- `500 Internal Server Error`: Server error occurred

---

### 4. Get Jobs

**Endpoint**: `GET /dashboard/jobs`

**Description**: Retrieves job listings with filtering options for status and company.

**Authentication**: Required (Coach)

**Query Parameters**:
- `skip` (integer, optional): Number of records to skip for pagination (default: 0)
- `limit` (integer, optional): Maximum number of records to return (default: 20)
- `status` (string, optional): Filter by job status (default: "active")
- `company_id` (string, optional): Filter by specific company ID

**Response Model**: `list[dict]`

**Example Request**:
```
GET /dashboard/jobs?skip=0&limit=10&status=active&company_id=company-123
```

**Response Schema**:
```json
[
  {
    // Job objects from Cosmos DB
    "id": "string",
    "title": "string",
    "company": "string",
    "location": "string",
    "status": "string",
    "posted_date": "string",
    // ... other job fields
  }
]
```

**Common Job Status Values**:
- `active`
- `closed`
- `draft`
- `expired`

**Example Response**:
```json
[
  {
    "id": "job-456",
    "title": "Senior Software Engineer",
    "company": "TechCorp Inc.",
    "company_id": "company-123",
    "location": "Remote",
    "status": "active",
    "posted_date": "2024-01-10T09:00:00Z",
    "description": "We are looking for a senior software engineer...",
    "requirements": ["5+ years experience", "Python", "AWS"],
    "salary_range": "$120,000 - $160,000"
  }
]
```

**Error Responses**:
- `401 Unauthorized`: Invalid or missing authentication token
- `500 Internal Server Error`: Server error occurred

---

## Data Sources

The Dashboard API integrates data from multiple sources:

1. **PostgreSQL Database**: 
   - Coach information
   - Client-coach relationships
   - Authentication data

2. **Cosmos DB - Profiles Container** (`dots-profile-test`):
   - Detailed talent profiles
   - Job search preferences
   - Skills and experience data

3. **Cosmos DB - Jobs Container** (`dots-biz-jobs`):
   - Job listings
   - Company information
   - Job requirements and descriptions

---

## Authentication

All endpoints require JWT authentication. The token should be included in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

The token is validated to ensure:
- Token is valid and not expired
- User exists in the system
- User has coach role permissions

---

## Error Handling

All endpoints follow consistent error response patterns:

**401 Unauthorized**:
```json
{
  "detail": "Could not validate credentials"
}
```

**500 Internal Server Error**:
```json
{
  "detail": "Internal server error"
}
```

**Logging**: All errors are logged with appropriate detail levels for debugging and monitoring.

---

## Rate Limiting

Currently, no rate limiting is implemented, but it's recommended to implement rate limiting for production use.

---

## Pagination

Endpoints that return lists support pagination through `skip` and `limit` parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 20)

Example: `GET /dashboard/my-clients?skip=20&limit=10` returns records 21-30.

---

## Performance Considerations

1. **Cosmos DB Queries**: The API makes asynchronous calls to Cosmos DB for better performance
2. **Error Resilience**: If Cosmos DB is unavailable, PostgreSQL data is still returned where possible
3. **Logging**: Comprehensive logging for monitoring and debugging
4. **Connection Pooling**: Database connections are managed through dependency injection

---

## Dependencies

The Dashboard API depends on:
- FastAPI framework
- SQLAlchemy for PostgreSQL operations
- Azure Cosmos DB SDK for NoSQL operations
- Pydantic for data validation
- JWT for authentication 