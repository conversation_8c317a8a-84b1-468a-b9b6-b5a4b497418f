Of course. Here is the complete, production-level backend system design document, meticulously detailed to serve as a comprehensive blueprint for development.

---

# **Career Coach Central: Backend System Design**


## Table of Contents
1.  [Introduction](#1-introduction)
    1.1. [Purpose](#11-purpose)
    1.2. [Scope](#12-scope)
    1.3. [System Overview](#13-system-overview)
    1.4. [Core Technologies](#14-core-technologies)
2.  [System Architecture](#2-system-architecture)
    2.1. [High-Level Architecture](#21-high-level-architecture)
    2.2. [Component Breakdown](#22-component-breakdown)
3.  [Authentication & Security](#3-authentication--security)
    3.1. [Authentication Flow](#31-authentication-flow)
    3.2. [Authorization Model](#32-authorization-model)
    3.3. [Security Considerations](#33-security-considerations)
4.  [Database Design](#4-database-design)
    4.1. [PostgreSQL Schema (Application Data)](#41-postgresql-schema-application-data)
    4.2. [ADK Session Management (`DatabaseSessionService`)](#42-adk-session-management-databasesessionservice)
    4.3. [Azure Cosmos DB (Global Talent Pool)](#43-azure-cosmos-db-global-talent-pool)
5.  [API Specification (FastAPI)](#5-api-specification-fastapi)
    5.1. [Authentication Endpoints](#51-authentication-endpoints)
    5.2. [Dashboard Endpoints](#52-dashboard-endpoints)
    5.3. [Chat WebSocket Endpoint](#53-chat-websocket-endpoint)
    5.4. [Report Endpoints](#54-report-endpoints)
6.  [Agent Architecture (Google ADK)](#6-agent-architecture-google-adk)
    6.1. [Core ADK Principles Applied](#61-core-adk-principles-applied)
    6.2. [Session Service Initialization](#62-session-service-initialization)
    6.3. [Agent Design: Career Coach Agent (CCA)](#63-agent-design-career-central-agent-cca)
    6.4. [Agent Design: Talent Agent (TA)](#64-agent-design-talent-agent-ta)
    6.5. [Agent Design: Recruiter Agent (RA)](#65-agent-design-recruiter-agent-ra)
    6.6. [Tool Design](#66-tool-design)
7.  [Detailed Workflow: "Generate a Report for a Client"](#7-detailed-workflow-generate-a-report-for-a-client)
8.  [Logging, Monitoring & Error Handling](#8-logging-monitoring--error-handling)
    8.1. [Logging](#81-logging)
    8.2. [Monitoring](#82-monitoring)
    8.3. [Error Handling](#83-error-handling)
9.  [Deployment & Operations](#9-deployment--operations)
    9.1. [Containerization](#91-containerization)
    9.2. [Environment Configuration](#92-environment-configuration)
    9.3. [CI/CD Pipeline](#93-ci-cd-pipeline)

---

## 1. Introduction

### 1.1. Purpose
This document provides a complete and detailed technical design for the backend system of the **Career Coach Central (CCA)** platform. It is intended for software engineers, architects, and DevOps teams to guide the implementation, deployment, and maintenance of the system.

### 1.2. Scope
This document covers all backend components, including:
*   The API server built with FastAPI.
*   Authentication and authorization mechanisms.
*   Database schemas and interactions for PostgreSQL and Azure Cosmos DB.
*   The design and orchestration of AI agents using the Google Agent Development Kit (ADK).
*   Non-functional requirements such as security, logging, monitoring, and deployment.

**Out of Scope:** Frontend implementation details, specific cloud infrastructure provisioning scripts, and detailed UI/UX design.

### 1.3. System Overview
The CCA backend is a sophisticated, AI-driven service that empowers career coaches. It provides a conversational interface through which coaches can manage their clients, gain insights into the job market, and automatically generate comprehensive career development reports. The system's core is a multi-agent architecture where a central orchestrator agent delegates specialized tasks to other expert agents, ensuring a modular and scalable design.

### 1.4. Core Technologies
*   **Web Framework:** FastAPI
*   **Authentication:** Firebase Authentication (Google Provider only)
*   **Databases:**
    *   **PostgreSQL:** Primary store for application data, user data, and ADK-managed conversation sessions.
    *   **Azure Cosmos DB:** Read-only data source for the global talent pool. 
*   **Agent Framework:** Google Agent Development Kit (ADK)
*   **Pacakge Management** uv
*   **Lint** ruff

## 2. System Architecture

### Folder Structure
career-coach-central-backend/
├── .env.example                # Example environment variables file
├── .gitignore                  # Git ignore file
├── Dockerfile                  # For containerizing the application
├── pyproject.toml              # Project metadata and dependencies (e.g., using Poetry)
├── README.md                   # Project overview, setup, and deployment instructions
│
├── agent/                      # << ADK MAIN ENTRANCE (as requested)
│   ├── __init__.py
│   └── agent.py                # Defines and exports the root `career_central_agent`
│
├── app/                        # Main application source code (FastAPI and supporting logic)
│   ├── __init__.py
│   ├── api/                    # FastAPI endpoints (routers)
│   │   ├── __init__.py
│   │   ├── auth.py             # Handles /auth/login endpoint
│   │   ├── dashboard.py        # Handles /dashboard/summary endpoint
│   │   ├── reports.py          # Handles /report endpoints
│   │   └── chat.py             # Handles the /chat WebSocket endpoint
│   │
│   ├── sub_agents/             # << RENAMED from 'agents' to avoid confusion
│   │   ├── __init__.py
│   │   ├── ta.py               # Talent Agent (TA) definition and prompt
│   │   └── ra.py               # Recruiter Agent (RA) definition and prompt
│   │
│   ├── core/                   # Core application logic and configuration
│   │   ├── __init__.py
│   │   ├── config.py           # Pydantic settings model to load environment variables
│   │   ├── security.py         # JWT creation, decoding, and dependency logic
│   │
│   ├── db/                     # Database interaction layer
│   │   ├── __init__.py
│   │   ├── models.py           # SQLAlchemy models for PostgreSQL tables
│   │   ├── schemas.py          # Pydantic schemas for data validation and serialization
│   │   └── crud.py             # Reusable functions for database operations
│   │
│   ├── services/               # Business logic and external service integrations
│   │   ├── __init__.py
│   │   ├── cosmos_client.py    # Client for interacting with Azure Cosmos DB
│   │   └── report_generator.py # Logic for generating HTML reports
│   │
│   ├── tools/                  # ADK FunctionTool definitions
│   │   ├── __init__.py
│   │   ├── db_tools.py         # Tools for querying PostgreSQL (job market, clients)
│   │   ├── cosmos_tools.py     # Tool for fetching profiles from Cosmos DB
│   │   └── report_tools.py     # Tool for saving the generated report
│   │
│   └── main.py                 # FastAPI application entry point, mounts routers, sets up middleware
│
├── docs/                       # Project documentation
│   ├── adk_examples.md         # Reference ADK examples (as provided)
│   ├── ARCHITECTURE.md         # High-level architecture overview
│   ├── API_GUIDE.md            # Detailed API endpoint documentation
│   ├── DEPLOYMENT.md           # Step-by-step deployment instructions
│   └── SYSTEM_DESIGN.md        # The complete system design document
│
├── sql/                        # << SIMPLIFIED SQL directory
│   ├── schema.sql              # Contains all CREATE TABLE statements for manual tables
│   └── initial_data.sql        # Optional script to seed initial data (e.g., job_market_data)
│
└── tests/                      # Automated tests
    ├── __init__.py
    ├── conftest.py             # Pytest fixtures and test setup
    ├── test_api/               # Tests for API endpoints
    │   ├── __init__.py
    │   └── test_auth_api.py
    ├── test_agents/            # Tests for agent logic and tool usage
    │   ├── __init__.py
    │   └── test_cca_workflow.py
    └── test_db/                # Tests for database CRUD operations
        ├── __init__.py
        └── test_crud.py

### 2.1. High-Level Architecture
The system follows a containerized microservice pattern, with a central FastAPI application acting as the API gateway and agent orchestrator.

```mermaid
graph TD
    subgraph "User's Browser"
        UI[React/Vue Frontend]
    end

    subgraph "Cloud Infrastructure (e.g., Google Cloud Run, AWS Fargate)"
        subgraph "Backend Container"
            A[FastAPI Application]
            ADK_SS[ADK DatabaseSessionService]
            subgraph "ADK Agent Layer"
                CCA[Career Coach Agent]
                TA[Talent Agent]
                RA[Recruiter Agent]
            end
        end
    end

    subgraph "Google Cloud Platform"
        F[Firebase Authentication]
    end

    subgraph "Database Services (e.g., Cloud SQL, Azure Cosmos DB)"
        P[PostgreSQL DB]
        C[Azure Cosmos DB]
    end

    UI -- HTTPS/REST API --> A
    UI -- WebSocket (WSS) --> A
    A -- Verify ID Token --> F
    A -- CRUD (Application Tables) --> P
    A -- Read-Only --> C[Read Global Talent Profiles]

    A -- Uses --> ADK_SS
    ADK_SS -- CRUD (ADK Session Tables) --> P

    A -- Invokes --> CCA
    CCA -- Operates within Session Context --> ADK_SS
    CCA -- Uses AgentTool --> TA
    CCA -- Uses AgentTool --> RA
    CCA -- Uses Function Tools to query --> P
    TA -- Uses Function Tools to query --> C
```

### 2.2. Component Breakdown
*   **FastAPI Application:** The main entry point. It exposes all REST and WebSocket endpoints, handles request validation (via Pydantic), and orchestrates the agent interactions.
*   **ADK `DatabaseSessionService`:** A core component of the ADK, integrated into the FastAPI app. It manages the entire lifecycle of conversations, persisting session state and event history directly into the PostgreSQL database.
*   **ADK Agent Layer:** Contains the definitions and logic for the three agents (CCA, TA, RA), their prompts, and their associated tools.
*   **Databases:** Decoupled data storage. PostgreSQL holds relational application data, while Cosmos DB holds NoSQL document data for talent profiles.

## 3. Authentication & Security

### 3.1. Authentication Flow
The system uses a secure token-based authentication flow.

1.  **Frontend:** The client-side application uses the Firebase SDK to initiate a Google Sign-In flow.
2.  **Firebase:** Upon successful authentication, Firebase returns a short-lived JWT (ID Token) to the client.
3.  **Token Exchange:** The client sends this Firebase ID Token to the backend's `POST /auth/login` endpoint in the `Authorization: Bearer <ID_TOKEN>` header.
4.  **Backend Verification:** The FastAPI backend uses the `firebase-admin` SDK to verify the signature and claims of the ID Token. This confirms the user's identity with Google's authority.
5.  **User Provisioning:** If the token is valid, the backend extracts the user's Firebase UID. It queries the `coaches` table to find or create a user record associated with this UID.
6.  **Session Token Issuance:** The backend generates its own long-lived, stateless JWT (Session Token), containing the internal `coach.id` and `coach.firebase_uid` as claims. This token is signed with a secret key known only to the backend.
7.  **Client Storage:** The frontend receives and stores this Session Token (e.g., in `localStorage` or a secure cookie) and includes it in the `Authorization` header for all subsequent requests to the backend.

### 3.2. Authorization Model
Authorization is enforced at the API level using a FastAPI dependency.

*   A dependency function will decode the Session Token from the `Authorization` header on every protected endpoint.
*   It will extract the `coach.id` from the token's payload.
*   All database queries that access user-specific data (e.g., clients, reports) **MUST** include a `WHERE coach_id = :current_coach_id` clause to ensure data tenancy and prevent users from accessing each other's data.

### 3.3. Security Considerations
*   **Secrets Management:** All sensitive credentials (DB connection strings, API keys, JWT secret) **MUST** be managed via environment variables and injected at runtime. They **MUST NOT** be hardcoded in the source code. Use a secrets manager like Google Secret Manager or HashiCorp Vault in production.
*   **Input Validation:** FastAPI's use of Pydantic models for request bodies provides automatic, robust input validation, preventing many common injection-style attacks.
*   **CORS:** Configure FastAPI's CORS middleware to only allow requests from the specific domain(s) of the frontend application.
*   **HTTPS Enforcement:** The production environment **MUST** be configured to only accept connections over HTTPS (WSS for WebSockets). This is typically handled by the load balancer or ingress controller.

## 4. Database Design

### 4.1. PostgreSQL Schema (Application Data)
These tables are explicitly defined and managed by the application logic using an ORM like SQLAlchemy.

```sql
-- This script should be managed by a migration tool like Alembic.

-- Enable UUID extension for unique identifiers
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Stores information about the career coaches (users of the platform)
CREATE TABLE coaches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    firebase_uid VARCHAR(128) UNIQUE NOT NULL, -- The unique identifier from Firebase Authentication
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_login_at TIMESTAMPTZ,
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Links coaches to their clients, whose full profiles are in Cosmos DB.
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    coach_id UUID NOT NULL REFERENCES coaches(id) ON DELETE CASCADE,
    cosmos_user_id UUID UNIQUE NOT NULL, -- The 'userId' from the Cosmos DB profile JSON
    cosmos_profile_id UUID UNIQUE NOT NULL, -- The 'id' (profile id) from the Cosmos DB profile JSON
    added_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    client_full_name VARCHAR(255), -- Denormalized for quick display on dashboards
    -- A coach cannot have the same client listed more than once.
    UNIQUE(coach_id, cosmos_user_id)
);

-- Stores curated job market data for the agents to query.
CREATE TABLE job_market_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trend_title VARCHAR(255) NOT NULL,
    summary TEXT NOT NULL,
    source_url VARCHAR(512),
    data_type VARCHAR(50) NOT NULL CHECK (data_type IN ('TREND', 'SALARY_INFO', 'SKILL_DEMAND')),
    region VARCHAR(100) NOT NULL DEFAULT 'Global',
    industry VARCHAR(100),
    recorded_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Stores the final generated career reports.
CREATE TABLE reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    coach_id UUID NOT NULL REFERENCES coaches(id) ON DELETE CASCADE,
    report_content TEXT NOT NULL, -- The full, rendered HTML content of the report
    access_token VARCHAR(64) UNIQUE NOT NULL DEFAULT REPLACE(uuid_generate_v4()::TEXT, '-', ''),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    sent_at TIMESTAMPTZ, -- Tracks if/when the report was sent to the client
    -- The session ID from which this report was generated, for traceability.
    source_session_id VARCHAR(255)
);

-- Create indexes for query performance optimization.
CREATE INDEX idx_clients_coach_id ON clients(coach_id);
CREATE INDEX idx_reports_client_id ON reports(client_id);
CREATE INDEX idx_reports_coach_id ON reports(coach_id);
CREATE INDEX idx_reports_access_token ON reports(access_token);
```

### 4.2. ADK Session Management (`DatabaseSessionService`)
The system will **not** have manually defined `chat_sessions` or `chat_messages` tables. Instead, the ADK `DatabaseSessionService` will be configured with the PostgreSQL connection string. It will automatically generate and manage its own schema (typically tables like `adk_sessions` and `adk_events`) to handle all aspects of conversation persistence, including history and state. This ensures robust, framework-native session management.

### 4.3. Azure Cosmos DB (Global Talent Pool)
This database is treated as an external, read-only source. The application will connect to it to fetch full client profiles based on the `cosmos_user_id` stored in the `clients` table. The provided JSON structure is the expected schema.

## 5. API Specification (FastAPI)

### 5.1. Authentication Endpoints
*   **`POST /auth/login`**
    *   **Description:** Exchanges a Firebase ID Token for a backend Session Token.
    *   **Request Header:** `Authorization: Bearer <FIREBASE_ID_TOKEN>`
    *   **Success (200):** `{ "session_token": "...", "token_type": "bearer" }`
    *   **Error (401):** `{ "detail": "Invalid or expired Firebase token" }`

### 5.2. Dashboard Endpoints
*   **`GET /dashboard/summary`**
    *   **Description:** Retrieves summary data for the coach's dashboard.
    *   **Request Header:** `Authorization: Bearer <SESSION_TOKEN>`
    *   **Success (200):** `{ "global_talent_pool_count": 150000, "my_clients_count": 25, "active_agents": [...] }`
    *   **Error (401):** `{ "detail": "Invalid session token" }`

### 5.3. Chat WebSocket Endpoint
*   **`WS /chat`**
    *   **Description:** Establishes a real-time, bidirectional communication channel for conversations with the agent system.
    *   **Connection:** The client must provide the Session Token during the WebSocket handshake for authentication.
    *   **Message Protocol (JSON):**
        *   **Client to Server:** `{ "type": "user_message", "content": "Text from user" }`
        *   **Server to Client (Agent Thinking):** `{ "type": "status", "message": "Analyzing your request..." }`
        *   **Server to Client (Agent Response):** `{ "type": "agent_message", "content": "Text from agent" }`
        *   **Server to Client (Report Ready):** `{ "type": "report_link", "content": "The report is ready.", "url": "/report/uuid?token=xyz" }`
        *   **Server to Client (Error):** `{ "type": "error", "message": "An error occurred while processing your request." }`

### 5.4. Report Endpoints
*   **`GET /report/{report_id}`**
    *   **Description:** Retrieves the public HTML content of a generated report. This is an unauthenticated endpoint.
    *   **Query Params:** `?token=<ACCESS_TOKEN>` (required)
    *   **Success (200):** `Content-Type: text/html`. The raw HTML content.
    *   **Error (401/404):** `{ "detail": "Invalid token or report not found" }`

*   **`POST /report/{report_id}/send`**
    *   **Description:** Marks a report as sent and can trigger a notification (e.g., email).
    *   **Request Header:** `Authorization: Bearer <SESSION_TOKEN>`
    *   **Success (202):** `{ "message": "Report sending process initiated." }`
    *   **Error (401/404):** `{ "detail": "Not authorized or report not found" }`

## 6. Agent Architecture (Google ADK)

### 6.1. Core ADK Principles Applied
*   **`DatabaseSessionService`:** All conversations are persisted in PostgreSQL, managed by the ADK framework.
*   **`session.state`:** Used as the primary mechanism for passing data between tool calls and agent reasoning steps within a single conversation turn.
*   **`AgentTool`:** The CCA uses this to wrap and call the TA and RA, enabling a hierarchical, multi-agent system.
*   **`FunctionTool`:** Used for discrete, non-agent tasks like querying the database or fetching a profile.

### 6.2. Session Service Initialization
This service is initialized once when the FastAPI application starts.

```python
# In main.py or a config file
from google.adk.sessions import DatabaseSessionService
import os

# This instance will be shared across the application.
# The DSN is loaded securely from an environment variable.
session_service = DatabaseSessionService(db_url=os.getenv("POSTGRES_DSN"))

# In FastAPI startup event handler:
@app.on_event("startup")
async def startup_event():
    # This command creates the necessary ADK tables if they do not exist.
    await session_service.aigenerate_db()
    print("ADK DatabaseSessionService initialized.")
```

### 6.3. Agent Design: Career Coach Agent (CCA)
*   **Type:** `google.adk.agents.Agent`
*   **Name:** `career_central_agent`
*   **Description:** The primary orchestrator that interacts with the user and delegates tasks to specialized agents.
*   **Instruction (Prompt):**
    ```
    You are the Career Coach Agent, a master AI assistant for professional career coaches. Your purpose is to understand the coach's commands and orchestrate a team of expert agents to provide answers and generate reports.

    # Primary Workflow
    1.  Analyze the user's request to determine their intent.
    2.  Use the available tools to fulfill the request. You MUST choose a tool.
    3.  If the request is ambiguous, ask for clarification.

    # Tool Usage Guidelines
    -   For general job market questions (e.g., "how's the market?"): Use the `get_job_market_data_tool`.
    -   For questions about the coach's client portfolio (e.g., "how are my clients?"): Use the `get_coach_clients_summary_tool`.
    -   To generate a detailed report for a specific client (e.g., "generate a report for [client name]"):
        a. You MUST first use the `get_client_by_name_tool` to retrieve the client's internal IDs.
        b. With the IDs, you MUST then call the `talent_agent_tool` to get a detailed profile analysis. The result will be stored in the session state.
        c. Next, you MUST call the `recruiter_agent_tool` to find matching job opportunities. The result will also be stored in the session state.
        d. Finally, with all information gathered in the session state, you MUST call the `create_and_save_report_tool` to compile and save the final report.
    4.  After a report is created, inform the user and provide the link.
    ```
*   **Tools:** `get_job_market_data_tool`, `get_coach_clients_summary_tool`, `get_client_by_name_tool`, `create_and_save_report_tool`, `AgentTool(agent=talent_agent)`, `AgentTool(agent=recruiter_agent)`.

### 6.4. Agent Design: Talent Agent (TA)
*   **Type:** `google.adk.agents.Agent`
*   **Name:** `talent_agent`
*   **Description:** An expert in analyzing a single talent's profile to identify strengths, weaknesses, and development opportunities.
*   **Instruction (Prompt):**
    ```
    You are a specialist Talent Analyst agent. Your input is a talent's unique ID. Your task is to fetch their complete professional profile and produce a structured analysis.

    # Your Process
    1.  Use the `get_client_profile_from_cosmos_tool` with the provided ID to retrieve the full profile data.
    2.  Analyze the 'workExperience', 'skillsAndExpertise', and 'education' sections of the profile.
    3.  Generate a response with two distinct sections in Markdown format:
        -   `## Personal Ability Analysis`: A summary of the candidate's key strengths, potential skill gaps, and career trajectory based on their history.
        -   `## Career Development Plan`: A list of 3-5 actionable next steps for the candidate, such as specific courses, certifications, or types of projects to seek out.
    ```
*   **Tools:** `get_client_profile_from_cosmos_tool`.

### 6.5. Agent Design: Recruiter Agent (RA)
*   **Type:** `google.adk.agents.Agent`
*   **Name:** `recruiter_agent`
*   **Description:** An expert in sourcing and evaluating job opportunities from the market that align with a candidate's profile.
*   **Instruction (Prompt):**
    ```
    You are a specialist Recruiter Agent. You will be given a summary of a candidate's profile (e.g., current job title, key skills).

    # Your Task
    1.  Use the `find_matching_jobs_tool` to search for relevant job openings based on the provided profile summary.
    2.  From the search results, select the top 3-5 most relevant opportunities.
    3.  For each selected opportunity, provide a concise summary including: Job Title, Company, and a brief "Justification" explaining why it is a strong match for the candidate's skills and experience.
    4.  Format your output as a Markdown list.
    ```
*   **Tools:** `find_matching_jobs_tool`.

### 6.6. Tool Design
*   **`get_client_by_name_tool(name: str, tool_context: ToolContext)`:** Executes `SELECT id, cosmos_user_id, cosmos_profile_id FROM clients WHERE client_full_name ILIKE :name AND coach_id = :coach_id`. The `:coach_id` is retrieved from the authenticated session.
*   **`get_client_profile_from_cosmos_tool(cosmos_user_id: str)`:** Connects to Azure Cosmos DB and fetches the full JSON document where `userId` matches the input.
*   **`create_and_save_report_tool(tool_context: ToolContext)`:** Reads `tool_context.state['client_analysis']` and `tool_context.state['job_matches']`. Compiles them into an HTML template. `INSERT`s the result into the `reports` table, including the `source_session_id` from `tool_context.session_id`. Returns the public URL.

## 7. Detailed Workflow: "Generate a Report for a Client"

1.  **Authentication & Session Start:** Coach connects to `WS /chat`. The server authenticates the token, gets the `coach_id`, and calls `session_service.create_session(user_id=coach_id, ...)` to get a new `Session` object.
2.  **User Input:** Coach sends: `{"type": "user_message", "content": "generate a report for Eleanor Vance"}`. The server appends this as an `Event` to the session.
3.  **CCA Invocation:** The `career_central_agent.run(session)` is called.
4.  **Turn 1: Get Client ID:**
    *   **CCA Reason:** Needs client ID for "Eleanor Vance".
    *   **CCA Act:** Calls `get_client_by_name_tool("Eleanor Vance")`.
    *   **Tool Result:** `{'client_id': 'uuid-clients-1', 'cosmos_user_id': 'dcdf...', 'cosmos_profile_id': 'c08b...'}`.
    *   **State Update:** The tool's wrapper function stores this dict in `session.state['current_client_ids']`.
5.  **Turn 2: Analyze Talent:**
    *   **CCA Reason:** Has client ID, needs profile analysis.
    *   **CCA Act:** Calls `AgentTool(talent_agent)` with the `cosmos_user_id`.
    *   **TA Execution:** The TA runs its full process and returns the formatted Markdown analysis.
    *   **State Update:** The `AgentTool` wrapper stores the TA's response text in `session.state['client_analysis']`.
6.  **Turn 3: Find Jobs:**
    *   **CCA Reason:** Has analysis, needs job matches.
    *   **CCA Act:** Calls `AgentTool(recruiter_agent)` with a summary of the client's profile.
    *   **RA Execution:** The RA runs its process and returns the formatted list of jobs.
    *   **State Update:** The `AgentTool` wrapper stores the RA's response in `session.state['job_matches']`.
7.  **Turn 4: Create Report:**
    *   **CCA Reason:** Has all necessary components in `session.state`.
    *   **CCA Act:** Calls `create_and_save_report_tool()`.
    *   **Tool Execution:** The tool reads from `session.state`, generates HTML, saves to the `reports` table, and returns the URL.
8.  **Final Response:**
    *   **CCA Reason:** The workflow is complete.
    *   **CCA Act:** Generates the final message for the user.
    *   **Server Action:** The agent's final response event is appended to the session, persisting all state changes from the turn. The server sends the message to the client: `{"type": "report_link", "url": "..."}`.

## 8. Logging, Monitoring & Error Handling

### 8.1. Logging
*   **Format:** Structured logging (JSON) is mandatory for easy parsing by log management systems (e.g., Google Cloud Logging, ELK stack).
*   **Content:** Each log entry should include a timestamp, log level, message, and a context dictionary.
*   **Key Events to Log:**
    *   Incoming API requests (method, path, IP).
    *   Authentication successes and failures.
    *   Database query errors.
    *   Agent invocation start and end, including the agent name and session ID.
    *   Tool calls made by agents, including parameters and success/failure status.
    *   Unhandled exceptions (with full stack traces).

### 8.2. Monitoring
*   **Metrics:** A `/metrics` endpoint should be exposed for a Prometheus scraper.
*   **Key Metrics to Track:**
    *   API request latency (by endpoint).
    *   API request count and error rate (by endpoint and status code).
    *   Agent execution latency (by agent name).
    *   Tool execution latency (by tool name).
    *   Active WebSocket connections.
*   **Dashboards:** Monitoring dashboards (e.g., in Grafana, Cloud Monitoring) should be created to visualize these metrics and set up alerts for anomalies (e.g., spike in latency or error rate).

### 8.3. Error Handling
*   **API Errors:** FastAPI will automatically convert Pydantic validation errors into 422 responses. Custom exception handlers should be implemented for other common errors (e.g., 401 for auth, 404 for not found) to ensure consistent JSON error responses.
*   **Agent/Tool Errors:** Errors within a tool or agent execution should be caught gracefully. The error should be logged in detail, and a user-friendly error message should be sent back through the WebSocket (e.g., `{"type": "error", "message": "I was unable to retrieve job market data at this time. Please try again later."}`). The agent's state should not be left corrupted.

## 9. Deployment & Operations

### 9.1. Containerization
The application will be packaged as a Docker image. The `Dockerfile` will be multi-stage to create a lean production image.

```dockerfile
# Stage 1: Build stage
FROM python:3.13-slim as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .

# Stage 2: Production stage
FROM python:3.13-slim
WORKDIR /app
COPY --from=builder /app .
# Set environment variables via the deployment platform
# EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 9.2. Environment Configuration
The following environment variables **MUST** be configured in the deployment environment:
*   `POSTGRES_DSN`: `postgresql+asyncpg://user:password@host:port/dbname`
*   `COSMOS_ENDPOINT`: The URI for the Azure Cosmos DB account.
*   `COSMOS_KEY`: The primary access key for Cosmos DB.
*   `COSMOS_DATABASE_NAME`: The name of the database to use.
*   `COSMOS_CONTAINER_NAME`: The name of the container holding talent profiles.
*   `GOOGLE_APPLICATION_CREDENTIALS`: Path to the GCP service account JSON file (for Firebase Admin).
*   `GEMINI_API_KEY`: The API key for the Gemini model used by the ADK.
*   `SESSION_TOKEN_SECRET_KEY`: A long, random, high-entropy string for signing session JWTs.
*   `CORS_ORIGINS`: A comma-separated list of allowed frontend origins (e.g., `https://app.careercoach.com`).

### 9.3. CI/CD Pipeline
A CI/CD pipeline (e.g., using GitHub Actions, GitLab CI) should be established to automate:
1.  **Linting & Static Analysis:** On every push/merge request.
2.  **Unit & Integration Tests:** Run the test suite.
3.  **Build Docker Image:** If tests pass on the main branch.
4.  **Push Image to Registry:** (e.g., Google Artifact Registry, Docker Hub).
5.  **Deploy to Staging:** Automatically deploy to a staging environment.
6.  **Deploy to Production:** A manual approval step to promote the staging build to production.