[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "career-coach-central-backend"
version = "0.1.0"
description = "An AI-driven service that empowers career coaches, providing a conversational interface to manage clients, gain job market insights, and generate career development reports."
requires-python = ">=3.13"

dependencies = [
    "fastapi",
    "uvicorn[standard]",
    "pydantic[email]",
    "pydantic-settings",
    "sqlalchemy",
    "psycopg2-binary",
    "asyncpg",
    "firebase-admin",
    "google-adk",
    "azure-search-documents",
    "azure-cosmos",
    "python-jose[cryptography]",
    "passlib[bcrypt]",
    "greenlet>=3.2.3",
]

[project.optional-dependencies]
dev = [
    "ruff",
    "pytest",
    "pytest-asyncio",
    "httpx",
]

[tool.setuptools.packages.find]
include = ["app*", "agent*"]
exclude = ["tests*", "sql*", "docs*"]
