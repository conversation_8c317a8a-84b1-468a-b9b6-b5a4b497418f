COSMOS_ENDPOINT="your_cosmos_db_endpoint_here"
COSMOS_KEY="your_cosmos_db_key_here"
COSMOS_DATABASE_NAME="your_cosmos_db_database_name_here"
COSMOS_JOBS_CONTAINER="your_cosmos_db_jobs_container_here"
COSMOS_PROFILES_CONTAINER="your_cosmos_db_profiles_container_here"
POSTGRES_URI="your_postgres_uri_here"
POSTGRES_PASSWORD="your_postgres_password_here"
POSTGRES_USER="your_postgres_user_here"
POSTGRES_DB="your_postgres_db_here"
SECRET_KEY="your_secret_key_here"
ALGORITHM="your_algorithm_here"
FIREBASE_PROJECT_ID="your_firebase_project_id_here"
FIREBASE_CREDENTIALS="path/to/your/firebase/credentials.json"
BACKEND_CORS_ORIGINS='["http://localhost:3000"]'
AZURE_AI_SEARCH_API_KEY="your_azure_ai_search_api_key_here"
AZURE_AI_SEARCH_ENDPOINT="your_azure_ai_search_endpoint_here"
AZURE_AI_SEARCH_INDEX_NAME="your_azure_ai_search_index_name_here"
GOOGLE_GENAI_USE_VERTEXAI="FALSE"
GOOGLE_API_KEY="your_google_api_key_here"
ENVIRONMENT="development"
