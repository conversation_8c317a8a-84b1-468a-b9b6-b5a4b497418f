# Career Coach Central - Backend

An AI-driven service that empowers career coaches, providing a conversational interface to manage clients, gain job market insights, and generate career development reports.

## 🚀 Features

- **AI-Powered Conversational Interface**: Multi-agent system using Google Agent Development Kit (ADK)
- **Client Management**: Comprehensive client data management and tracking
- **Job Market Insights**: Real-time job market analysis and trends
- **Automated Report Generation**: AI-generated career development reports
- **Secure Authentication**: Firebase Authentication with Google provider
- **Real-time Chat**: WebSocket-based chat interface
- **Multi-Database Architecture**: PostgreSQL for application data, Azure Cosmos DB for global talent pool

## 🏗️ Architecture

### Core Components

- **FastAPI Application**: RESTful API server with WebSocket support
- **Multi-Agent System**: 
  - Career Coach Agent (CCA) - Main orchestrator
  - Talent Agent (TA) - Talent pool analysis
  - Recruiter Agent (RA) - Job market insights
- **Database Layer**: 
  - PostgreSQL for application data and ADK sessions
  - Azure Cosmos DB for global talent profiles
- **Authentication**: Firebase Authentication integration

### Technology Stack

- **Framework**: FastAPI
- **Agent System**: Google Agent Development Kit (ADK)
- **Databases**: PostgreSQL, Azure Cosmos DB
- **Authentication**: Firebase Authentication
- **Package Management**: uv/pip
- **Code Quality**: Ruff (linting)
- **Python**: 3.13+

## 📁 Project Structure

```
career-coach-central-backend/
├── agent/                      # ADK main entrance
│   ├── __init__.py
│   └── agent.py               # Root career_central_agent
├── app/                       # Main application
│   ├── api/                   # FastAPI endpoints
│   │   ├── auth.py           # Authentication endpoints
│   │   ├── dashboard.py      # Dashboard endpoints
│   │   ├── reports.py        # Report endpoints
│   │   └── chat.py           # WebSocket chat endpoint
│   ├── core/                 # Core configuration
│   │   ├── config.py         # Settings management
│   │   └── security.py       # JWT and security
│   ├── db/                   # Database layer
│   │   ├── models.py         # SQLAlchemy models
│   │   ├── schemas.py        # Pydantic schemas
│   │   └── crud.py           # Database operations
│   ├── services/             # Business logic
│   │   ├── cosmos_client.py  # Azure Cosmos DB client
│   │   └── report_generator.py # Report generation
│   ├── sub_agents/           # Agent definitions
│   │   ├── ta.py            # Talent Agent
│   │   └── ra.py            # Recruiter Agent
│   ├── tools/               # ADK function tools
│   │   ├── db_tools.py      # PostgreSQL tools
│   │   ├── cosmos_tools.py  # Cosmos DB tools
│   │   └── report_tools.py  # Report tools
│   └── main.py              # FastAPI application entry
├── docs/                    # Documentation
├── sql/                     # Database schemas
├── tests/                   # Test suite
└── Dockerfile              # Container configuration
```

## 🛠️ Installation

### Prerequisites

- Python 3.13+
- PostgreSQL database
- Azure Cosmos DB account
- Firebase project with Authentication enabled
- Google Cloud Platform account (for ADK)

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd career-coach-central-backend
   ```

2. **Install dependencies**
   ```bash
   pip install -e .
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Configure the following environment variables in `.env`:
   ```env
   # Database
   POSTGRES_DSN=postgresql://user:password@localhost:5432/career_coach_db
   
   # Firebase
   FIREBASE_PROJECT_ID=your-firebase-project-id
   FIREBASE_CREDENTIALS_PATH=path/to/firebase-credentials.json
   
   # Azure Cosmos DB
   COSMOS_DB_ENDPOINT=https://your-cosmos-account.documents.azure.com:443/
   COSMOS_DB_KEY=your-cosmos-db-key
   COSMOS_DB_DATABASE=talent-pool
   COSMOS_DB_CONTAINER=profiles
   
   # Security
   SECRET_KEY=your-secret-key
   ALGORITHM=HS256
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   
   # CORS
   BACKEND_CORS_ORIGINS=["http://localhost:3000"]
   ```

4. **Database Setup**
   ```bash
   # Create PostgreSQL database
   createdb career_coach_db
   
   # Run database migrations
   psql -d career_coach_db -f sql/schema.sql
   psql -d career_coach_db -f sql/initial_data.sql
   ```

## 🚀 Usage

### Development Server

```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at `http://localhost:8000`

### API Documentation

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

### Key Endpoints

#### Authentication
- `POST /auth/login` - Authenticate with Firebase ID token

#### Dashboard
- `GET /dashboard/summary` - Get dashboard summary data

#### Reports
- `POST /report/generate` - Generate career development report
- `GET /report/{report_id}` - Retrieve generated report

#### Chat
- `WS /chat/ws` - WebSocket connection for real-time chat

## 🔧 Development

### Code Quality

```bash
# Linting
ruff check .

# Formatting
ruff format .
```

### Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html
```

### Database Operations

```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head
```

## 🐳 Docker Deployment

### Build Image

```bash
docker build -t career-coach-central-backend .
```

### Run Container

```bash
docker run -p 8000:8000 \
  --env-file .env \
  career-coach-central-backend
```

### Docker Compose

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_DSN=**************************************/career_coach_db
    depends_on:
      - db
  
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=career_coach_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 🔒 Security

- **Authentication**: Firebase Authentication with Google provider only
- **Authorization**: JWT-based access control
- **CORS**: Configurable cross-origin resource sharing
- **Input Validation**: Pydantic schema validation
- **SQL Injection Protection**: SQLAlchemy ORM with parameterized queries

## 📊 Monitoring & Logging

The application includes comprehensive logging and monitoring:

- **Structured Logging**: JSON-formatted logs for production
- **Error Tracking**: Automatic error capture and reporting
- **Performance Metrics**: Request timing and database query metrics
- **Health Checks**: Built-in health check endpoints

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow PEP 8 style guidelines
- Write comprehensive tests for new features
- Update documentation for API changes
- Use type hints throughout the codebase

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- **Documentation**: Check the `/docs` directory for detailed documentation
- **Issues**: Create an issue on GitHub for bug reports or feature requests
- **API Reference**: Visit `/docs` endpoint when running the server

## 🗺️ Roadmap

- [ ] Enhanced AI agent capabilities
- [ ] Advanced analytics and reporting
- [ ] Mobile API optimization
- [ ] Multi-language support
- [ ] Integration with more job boards
- [ ] Advanced client management features

---

**Career Coach Central Backend** - Empowering career coaches with AI-driven insights and automation.
