from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db import crud, models, schemas
from app.core.security import get_current_user
from app.db.session import get_db
from app.services.cosmos_client import (
    get_job_cosmos_service,
    get_profile_cosmos_service,
    JobCosmosService,
    ProfileCosmosService,
)
from app.services.azure_search import AzureSearchService, get_azure_search_service
import logging

# Configure logger
logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/summary", response_model=schemas.DashboardSummary)
async def get_dashboard_summary(
    db: Session = Depends(get_db),
    current_user: models.Coach = Depends(get_current_user),
    job_cosmos_service: JobCosmosService = Depends(get_job_cosmos_service),
    profile_cosmos_service: ProfileCosmosService = Depends(get_profile_cosmos_service),
):
    """
    Retrieves summary data for the coach's dashboard.
    """
    try:
        # Get my clients count from PostgreSQL
        my_clients_count = len(crud.get_clients_by_coach(db, coach_id=current_user.id))

        # Get global talent pool count from profiles container
        try:
            global_talent_pool_count = await profile_cosmos_service.count_profiles()
        except Exception as e:
            logger.error(f"Error getting talent pool count: {e}")
            global_talent_pool_count = 0

        # Get active jobs count from jobs container
        try:
            active_jobs_count = await job_cosmos_service.count_jobs(status="active")
        except Exception as e:
            logger.error(f"Error getting active jobs count: {e}")
            active_jobs_count = 0

        # Placeholder for active agents
        active_agents = [{
            "name": "Career Coach Agent",
            "status": "active",
            "sub_agent": [
                {
                    "name": "Talent Agent",
                    "status": "active"
                },
                {
                    "name": "Recruiter Agent",
                    "status": "active"
                }
            ]
        }]

        return {
            "my_clients_count": my_clients_count,
            "global_talent_pool_count": global_talent_pool_count,
            "active_jobs_count": active_jobs_count,
            "active_agents": active_agents,
        }

    except Exception as e:
        logger.error(f"Error in dashboard summary: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/my-clients", response_model=list[dict])
async def get_my_clients(
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db),
    current_user: models.Coach = Depends(get_current_user),
    profile_cosmos_service: ProfileCosmosService = Depends(get_profile_cosmos_service),
):
    """
    Retrieves the coach's clients with their detailed profile information from Cosmos DB.
    """
    try:
        # Get clients from PostgreSQL
        clients = crud.get_clients_by_coach(
            db, coach_id=current_user.id, skip=skip, limit=limit
        )
        
        # Get detailed profile information for each client from Cosmos DB
        client_profiles = []
        for client in clients:
            try:
                # Get profile from Cosmos DB using cosmos_profile_id
                profile = await profile_cosmos_service.get_profile(str(client.cosmos_profile_id))
                
                if profile:
                    # Combine client info with profile data
                    client_profile = {
                        "client_id": str(client.id),
                        "cosmos_user_id": str(client.cosmos_user_id),
                        "cosmos_profile_id": str(client.cosmos_profile_id),
                        "client_full_name": client.client_full_name,
                        "added_at": client.added_at.isoformat(),
                        "profile": profile
                    }
                    client_profiles.append(client_profile)
                else:
                    # If profile not found in Cosmos, still include basic client info
                    client_profile = {
                        "client_id": str(client.id),
                        "cosmos_user_id": str(client.cosmos_user_id),
                        "cosmos_profile_id": str(client.cosmos_profile_id),
                        "client_full_name": client.client_full_name,
                        "added_at": client.added_at.isoformat(),
                        "profile": None
                    }
                    client_profiles.append(client_profile)
                    logger.warning(f"Profile not found in Cosmos for client {client.id}")
                    
            except Exception as e:
                logger.error(f"Error retrieving profile for client {client.id}: {e}")
                # Include client without profile data if there's an error
                client_profile = {
                    "client_id": str(client.id),
                    "cosmos_user_id": str(client.cosmos_user_id),
                    "cosmos_profile_id": str(client.cosmos_profile_id),
                    "client_full_name": client.client_full_name,
                    "added_at": client.added_at.isoformat(),
                    "profile": None
                }
                client_profiles.append(client_profile)
        
        return client_profiles
        
    except Exception as e:
        logger.error(f"Error retrieving my clients: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/talent-pool", response_model=list[dict])
async def get_talent_pool(
    skip: int = 0,
    limit: int = 20,
    job_search_status: str | None = None,
    preferred_industries: list[str] | None = None,
    current_user: models.Coach = Depends(get_current_user),
    profile_cosmos_service: ProfileCosmosService = Depends(get_profile_cosmos_service),
):
    """
    Retrieves talent pool data with filtering options.
    """
    try:
        profiles = await profile_cosmos_service.get_profiles(
            skip=skip,
            limit=limit,
            job_search_status=job_search_status,
            preferred_industries=preferred_industries,
        )
        return profiles
    except Exception as e:
        logger.error(f"Error retrieving talent pool: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/jobs", response_model=list[dict])
async def get_jobs(
    skip: int = 0,
    limit: int = 20,
    status: str | None = None,
    company_id: str | None = None,
    current_user: models.Coach = Depends(get_current_user),
    job_cosmos_service: JobCosmosService = Depends(get_job_cosmos_service),
):
    """
    Retrieves job listings with filtering options.
    """
    try:
        jobs = await job_cosmos_service.get_jobs(
            skip=skip,
            limit=limit,
            status=status,
            company_id=company_id,
        )
        return jobs
    except Exception as e:
        logger.error(f"Error retrieving jobs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/add-client", response_model=dict)
async def add_client_from_talent_pool(
    request: schemas.AddClientRequest,
    db: Session = Depends(get_db),
    current_user: models.Coach = Depends(get_current_user),
    profile_cosmos_service: ProfileCosmosService = Depends(get_profile_cosmos_service),
):
    """
    Adds a person from the talent pool as the coach's client.
    """
    try:
        # Check if the profile exists in Cosmos DB
        profile = await profile_cosmos_service.get_profile(str(request.cosmos_profile_id))
        if not profile:
            raise HTTPException(
                status_code=404, 
                detail="Profile not found in talent pool"
            )
        
        # Check if the profile is already a client of this coach
        existing_client = crud.get_client_by_cosmos_profile_id(
            db, request.cosmos_profile_id, current_user.id
        )
        if existing_client:
            raise HTTPException(
                status_code=400, 
                detail="This person is already your client"
            )
        
        # Extract user information from profile
        cosmos_user_id = profile.get("userId")
        if not cosmos_user_id:
            raise HTTPException(
                status_code=400, 
                detail="Profile does not have a valid user ID"
            )
        
        # Extract name from profile
        basic_info = profile.get("basicInfo", {})
        contact_info = profile.get("contactInfo", {})
        personal_info = profile.get("personalInfo", {})  # Keep as fallback
        
        # Try multiple approaches to extract the full name
        client_full_name = ""
        
        # Approach 1: fullName from basicInfo (primary approach)
        full_name = basic_info.get("fullName", "").strip()
        if full_name:
            client_full_name = full_name
        
        # Approach 2: preferredName from basicInfo
        elif basic_info.get("preferredName", "").strip():
            client_full_name = basic_info.get("preferredName", "").strip()
        
        # Approach 3: firstName + lastName from personalInfo (fallback)
        elif personal_info.get("firstName", "").strip() or personal_info.get("lastName", "").strip():
            first_name = personal_info.get("firstName", "").strip()
            last_name = personal_info.get("lastName", "").strip()
            client_full_name = f"{first_name} {last_name}".strip()
        
        # Approach 4: displayName from personalInfo
        elif personal_info.get("displayName", "").strip():
            client_full_name = personal_info.get("displayName", "").strip()
        
        # Approach 5: fullName from personalInfo
        elif personal_info.get("fullName", "").strip():
            client_full_name = personal_info.get("fullName", "").strip()
        
        # Approach 6: top-level name field
        elif profile.get("name", "").strip():
            client_full_name = profile.get("name", "").strip()
        
        # Approach 7: Use email username as last resort
        else:
            # Try to get email from contactInfo.emails array
            emails = contact_info.get("emails", [])
            email = ""
            if emails and len(emails) > 0:
                # Get the primary email or first email
                primary_email = next((e for e in emails if e.get("isPrimary")), None)
                email = primary_email.get("email", "") if primary_email else emails[0].get("email", "")
            
            # Fallback to personalInfo email
            if not email:
                email = personal_info.get("email", "")
            
            if email and "@" in email:
                username = email.split("@")[0]
                # Convert username to more readable format
                client_full_name = username.replace(".", " ").replace("_", " ").title()
            else:
                client_full_name = "Unknown User"
        
        logger.info(f"Extracted client name: '{client_full_name}' for profile {request.cosmos_profile_id}")
        
        # Create the client
        client_data = schemas.ClientCreate(
            cosmos_user_id=cosmos_user_id,
            cosmos_profile_id=request.cosmos_profile_id,
            client_full_name=client_full_name,
        )
        
        new_client = crud.create_client(db, client_data, current_user.id)
        
        return {
            "success": True,
            "message": "Client added successfully",
            "client": {
                "client_id": str(new_client.id),
                "cosmos_user_id": str(new_client.cosmos_user_id),
                "cosmos_profile_id": str(new_client.cosmos_profile_id),
                "client_full_name": new_client.client_full_name,
                "added_at": new_client.added_at.isoformat(),
            },
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding client from talent pool: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/search/jobs", response_model=dict)
async def search_jobs_with_azure_search(
    query: str,
    top: int = 20,
    skip: int = 0,
    filters: str | None = None,
    semantic_ranking: bool = True,
    current_user: models.Coach = Depends(get_current_user),
    azure_search_service: AzureSearchService = Depends(get_azure_search_service),
):
    """
    Search for jobs using Azure Cognitive Search with semantic ranking.
    
    Args:
        query: Search query string (keywords, job titles, skills, company names, etc.)
        top: Number of results to return (default: 20)
        skip: Number of results to skip for pagination (default: 0)
        filters: OData filter expression for additional filtering (e.g., "status eq 'active'")
        semantic_ranking: Whether to use semantic ranking for better relevance (default: True)
    
    Returns:
        Dictionary containing search results with job matches and metadata
    
    Examples:
        GET /search/jobs?query=Python developer
        GET /search/jobs?query=machine learning engineer&top=10
        GET /search/jobs?query=software engineer&filters=location eq 'San Francisco'
    """
    try:
        search_results = await azure_search_service.search_jobs_by_query(
            query=query,
            top=top,
            skip=skip,
            filters=filters,
            semantic_ranking=semantic_ranking
        )
        
        return search_results
        
    except Exception as e:
        logger.error(f"Error in Azure Search job query: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Job search failed: {str(e)}"
        )


@router.post("/search/jobs/profile-match", response_model=dict)
async def find_matching_jobs_by_profile(
    request: schemas.ProfileJobMatchRequest,
    current_user: models.Coach = Depends(get_current_user),
    azure_search_service: AzureSearchService = Depends(get_azure_search_service),
):
    """
    Find matching jobs for a candidate profile using Azure Search.
    
    Args:
        request: ProfileJobMatchRequest containing:
            - profile_keywords: List of keywords/skills from the candidate's profile
            - job_preferences: Optional dictionary with job preferences
            - top: Number of results to return (default: 20)
            - skip: Number of results to skip for pagination (default: 0)
            - boost_recent: Whether to prioritize recent job postings (default: True)
    
    Returns:
        Dictionary containing matching jobs with relevance scores and metadata
    
    Example request body:
    {
        "profile_keywords": ["Python", "Machine Learning", "TensorFlow"],
        "job_preferences": {
            "preferred_location": "San Francisco",
            "min_salary": 100000,
            "remote_work": true,
            "preferred_industries": ["Technology"]
        },
        "top": 20,
        "boost_recent": true
    }
    """
    try:
        search_results = await azure_search_service.find_matching_jobs_by_profile(
            profile_keywords=request.profile_keywords,
            job_preferences=request.job_preferences,
            top=request.top,
            skip=request.skip,
            boost_recent=request.boost_recent
        )
        
        return search_results
        
    except Exception as e:
        logger.error(f"Error in profile-based job matching: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Profile-based job matching failed: {str(e)}"
        )


@router.get("/search/jobs/suggestions", response_model=dict)
async def get_job_suggestions(
    partial_query: str,
    top: int = 10,
    current_user: models.Coach = Depends(get_current_user),
    azure_search_service: AzureSearchService = Depends(get_azure_search_service),
):
    """
    Get job suggestions for autocomplete functionality using Azure Search.
    
    Args:
        partial_query: Partial search query for suggestions (e.g., "soft", "data sci")
        top: Number of suggestions to return (default: 10)
    
    Returns:
        Dictionary containing job title suggestions with company and location info
    
    Examples:
        GET /search/jobs/suggestions?partial_query=software
        GET /search/jobs/suggestions?partial_query=data&top=5
    """
    try:
        suggestions = await azure_search_service.suggest_jobs(
            partial_query=partial_query,
            top=top
        )
        
        return suggestions
        
    except Exception as e:
        logger.error(f"Error getting job suggestions: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Job suggestions failed: {str(e)}"
        )

