from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from app.core.security import get_current_user
from app.db.session import get_db
from app.db import schemas
from agent.agent import root_agent
from google.adk.runners import Runner
from google.adk.sessions.database_session_service import DatabaseSessionService
from google.adk.memory import InMemoryMemoryService
from google.adk.artifacts import InMemoryArtifactService
from google.genai.types import Content, Part
import logging
import json
import base64
from typing import AsyncGenerator
from app.core.config import settings

# Configure logger
logger = logging.getLogger(__name__)

router = APIRouter()

APP_NAME = "career-coach-central"

# Initialize session service
session_service = DatabaseSessionService(settings.POSTGRES_DSN)

async def stream_agent_response(user_id: str, message: str, session_id: str, selected_client_id: str = None) -> AsyncGenerator[str, None]:
    """
    Enhanced agent response streaming that handles all ADK event types.
    
    Event types streamed to frontend:
    - turn_status: Turn completion/interruption signals
    - text_content: Text responses (partial streaming chunks)
    - final_text: Complete final text response (accumulated)
    - function_calls: Tool/function invocation requests
    - function_responses: Tool/function execution results
    - inline_data: Binary data (audio, images) as base64
    - actions: State updates, artifacts, auth configs
    - long_running_tools: Background tool execution status
    - metadata: Agent transfers and other metadata
    - final_response: Final response indicators
    - error: Error conditions
    
    All events include common metadata: id, timestamp, author, invocation_id
    """
    try:
        # Create a Runner
        runner = Runner(
            app_name=APP_NAME,
            agent=root_agent,
            artifact_service=InMemoryArtifactService(),
            session_service=session_service,
            memory_service=InMemoryMemoryService()
        )

        # Set state based on client selection
        state = {"coach_id": user_id}
        if selected_client_id:
            state["selected_client_id"] = selected_client_id

        session = await runner.session_service.get_session(
            app_name=APP_NAME,
            user_id=user_id,
            session_id=session_id
        )
        logger.info(f"Session: {session}")
        if not session:
            # Create session
            session = await runner.session_service.create_session(
                app_name=APP_NAME,
                user_id=user_id,
                state=state,
                session_id=session_id
            )

        # Create message content
        new_message = Content(
            role="user",
            parts=[Part.from_text(text=message)]
        )

        # Start agent execution
        events_iterator = runner.run_async(
            user_id=user_id,
            session_id=session.id,
            new_message=new_message,
        )

        # Process events and stream all types of responses
        accumulated_text = ""  # For accumulating streaming text
        async for event in events_iterator:
            # Extract common event metadata
            event_metadata = {
                "id": getattr(event, 'id', None),
                "timestamp": getattr(event, 'timestamp', None),
                "author": getattr(event, 'author', 'unknown'),
                "invocation_id": getattr(event, 'invocation_id', None)
            }
            
            # Handle turn completion or interruption
            if event.turn_complete or event.interrupted:
                response = {
                    "event_type": "turn_status",
                    "turn_complete": event.turn_complete,
                    "interrupted": event.interrupted,
                    **event_metadata
                }
                yield f"data: {json.dumps(response)}\n\n"
                logger.info(f"Turn complete for user {user_id}")
                break

            # Handle function calls (tool requests)
            if hasattr(event, 'content') and event.content and event.content.parts:
                function_calls = []
                function_responses = []
                
                for part in event.content.parts:
                    # Handle function calls
                    if hasattr(part, 'function_call') and part.function_call:
                        function_calls.append({
                            "id": part.function_call.id,
                            "name": part.function_call.name,
                            "args": part.function_call.args
                        })
                    
                    # Handle function responses
                    elif hasattr(part, 'function_response') and part.function_response:
                        function_responses.append({
                            "id": part.function_response.id,
                            "name": part.function_response.name,
                            "response": part.function_response.response
                        })
                    
                    # Handle text content
                    elif part.text:
                        is_partial = getattr(event, 'partial', False)
                        
                        # Accumulate text for final response
                        if is_partial:
                            accumulated_text += part.text
                        else:
                            # For final text, use accumulated + current or just current
                            final_text = accumulated_text + part.text if accumulated_text else part.text
                            accumulated_text = ""  # Reset for next message
                        
                        # Stream the text chunk
                        response = {
                            "event_type": "text_content",
                            "content": {
                                "mime_type": "text/plain",
                                "data": part.text,
                                "partial": is_partial
                            },
                            **event_metadata
                        }
                        yield f"data: {json.dumps(response)}\n\n"
                        
                        # Also send complete final message for non-partial text
                        if not is_partial:
                            final_response = {
                                "event_type": "final_text",
                                "content": {
                                    "mime_type": "text/plain",
                                    "data": final_text,
                                    "partial": False
                                },
                                **event_metadata
                            }
                            yield f"data: {json.dumps(final_response)}\n\n"
                            logger.info(f"Final complete text for user {user_id}: {final_text[:100]}...")
                        
                        if is_partial:
                            logger.debug(f"Partial text for user {user_id}: {part.text[:50]}...")
                        else:
                            logger.info(f"Final text chunk for user {user_id}: {part.text[:100]}...")
                    
                    # Handle inline data (audio, images, etc.)
                    elif hasattr(part, 'inline_data') and part.inline_data:
                        # Ensure data is base64 encoded for JSON transport
                        data = part.inline_data.data
                        if isinstance(data, bytes):
                            data = base64.b64encode(data).decode('ascii')
                        
                        response = {
                            "event_type": "inline_data",
                            "content": {
                                "mime_type": part.inline_data.mime_type,
                                "data": data,  # Base64 encoded data
                                "partial": getattr(event, 'partial', False)
                            },
                            **event_metadata
                        }
                        yield f"data: {json.dumps(response)}\n\n"
                        logger.info(f"Inline data for user {user_id}: {part.inline_data.mime_type}")

                # Stream function calls
                if function_calls:
                    response = {
                        "event_type": "function_calls",
                        "function_calls": function_calls,
                        **event_metadata
                    }
                    yield f"data: {json.dumps(response)}\n\n"
                    logger.info(f"Function calls for user {user_id}: {[fc['name'] for fc in function_calls]}")

                # Stream function responses
                if function_responses:
                    response = {
                        "event_type": "function_responses",
                        "function_responses": function_responses,
                        **event_metadata
                    }
                    yield f"data: {json.dumps(response)}\n\n"
                    logger.info(f"Function responses for user {user_id}: {[fr['name'] for fr in function_responses]}")

            # Handle actions (state updates, artifacts, etc.)
            if hasattr(event, 'actions') and event.actions:
                action_data = {}
                
                # Handle state delta
                if hasattr(event.actions, 'state_delta') and event.actions.state_delta:
                    action_data['state_delta'] = event.actions.state_delta
                
                # Handle artifact delta
                if hasattr(event.actions, 'artifact_delta') and event.actions.artifact_delta:
                    action_data['artifact_delta'] = event.actions.artifact_delta
                
                # Handle requested auth configs
                if hasattr(event.actions, 'requested_auth_configs') and event.actions.requested_auth_configs:
                    action_data['requested_auth_configs'] = event.actions.requested_auth_configs
                
                # Handle skip summarization
                if hasattr(event.actions, 'skip_summarization') and event.actions.skip_summarization:
                    action_data['skip_summarization'] = event.actions.skip_summarization

                if action_data:
                    response = {
                        "event_type": "actions",
                        "actions": action_data,
                        **event_metadata
                    }
                    yield f"data: {json.dumps(response)}\n\n"
                    logger.info(f"Actions for user {user_id}: {list(action_data.keys())}")

            # Handle long running tools
            if hasattr(event, 'long_running_tool_ids') and event.long_running_tool_ids:
                response = {
                    "event_type": "long_running_tools",
                    "long_running_tool_ids": event.long_running_tool_ids,
                    **event_metadata
                }
                yield f"data: {json.dumps(response)}\n\n"
                logger.info(f"Long running tools for user {user_id}: {event.long_running_tool_ids}")

            # Handle agent transfers or other metadata
            if hasattr(event, 'metadata') and event.metadata:
                response = {
                    "event_type": "metadata",
                    "metadata": event.metadata,
                    **event_metadata
                }
                yield f"data: {json.dumps(response)}\n\n"
                logger.info(f"Metadata for user {user_id}: {event.metadata}")

            # Handle any final response indicators
            if hasattr(event, 'is_final_response') and callable(event.is_final_response) and event.is_final_response():
                response = {
                    "event_type": "final_response",
                    "is_final": True,
                    **event_metadata
                }
                yield f"data: {json.dumps(response)}\n\n"
                logger.info(f"Final response indicator for user {user_id}")

    except Exception as e:
        logger.error(f"Error in agent stream for user {user_id}: {e}")
        error_response = {
            "event_type": "error",
            "error": "Stream error occurred",
            "details": str(e),
            "author": "system",
            "timestamp": None,
            "id": None,
            "invocation_id": None
        }
        yield f"data: {json.dumps(error_response)}\n\n"


@router.post("/stream")
async def sse_endpoint(
    request: schemas.StreamRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Enhanced SSE endpoint for real-time chat streaming with comprehensive ADK event handling.
    
    Streams all types of events from the career coach agent including:
    - Text content (partial streaming + complete final responses)
    - Function calls and responses
    - Agent transfers and metadata
    - State updates and artifacts
    - Inline data (audio, images, etc.)
    - Long running tool status
    - Turn completion signals
    """
    try:
        user_id = str(current_user.id)
        session_id = str(request.session_id)
        message = request.message
        selected_client_id = request.client_id[0] if request.client_id and len(request.client_id) > 0 else None

        if not message:
            raise HTTPException(status_code=400, detail="Message is required")

        logger.info(f"Starting stream for user {user_id}: {message[:100]}...")

        return StreamingResponse(
            stream_agent_response(user_id, message, session_id, selected_client_id),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control, Authorization",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected SSE error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Remove the complex message and interrupt endpoints since they're no longer needed
# with the simplified direct streaming approach
