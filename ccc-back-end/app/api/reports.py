import uuid
from fastapi import <PERSON><PERSON>outer, Depends, HTTPException, status
from fastapi.responses import PlainTextResponse
from sqlalchemy.orm import Session
from app.db import crud, models
from app.core.security import get_current_user
from app.db.session import get_db
from datetime import datetime

router = APIRouter()


@router.get("/{token}", response_class=PlainTextResponse)
def view_report(token: str, db: Session = Depends(get_db)):
    """
    Retrieves the markdown content of a generated report using only the access token.
    """
    report = crud.get_report_by_access_token(db, token=token)
    if not report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report not found",
        )
    
    return PlainTextResponse(content=report.report_content)


@router.post("/{report_id}/send")
def send_report(
    report_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: models.Coach = Depends(get_current_user),
):
    """
    Marks a report as sent and can trigger a notification (e.g., email).
    """
    report = crud.get_report(db, report_id=report_id)
    if not report or report.coach_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Not authorized or report not found",
        )

    if report.sent_at:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Report has already been sent.",
        )

    report.sent_at = datetime.utcnow()
    db.commit()

    # Here you could add logic to send an email notification

    return {"message": "Report sending process initiated."}
