from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON>2PasswordBearer
from sqlalchemy.orm import Session
from app.db import crud, schemas
from app.core import security
from app.db.session import get_db
import logging

# Configure logger
logger = logging.getLogger(__name__)

router = APIRouter()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")


@router.post("/login", response_model=schemas.Coach)
def login_for_access_token(
    token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)
):
    """
    Verify Firebase token and return user information.
    Since we now directly validate Firebase tokens, this endpoint
    just ensures the user exists in our database.
    """
    try:
        firebase_user = security.verify_firebase_token(token)
        firebase_uid = firebase_user["uid"]

        coach = crud.get_coach_by_firebase_uid(db, firebase_uid=firebase_uid)
        if not coach:
            # Create a new coach if one doesn't exist
            user_email = firebase_user.get("email")
            user_name = firebase_user.get("name")
            new_coach = schemas.CoachCreate(
                email=user_email, full_name=user_name, firebase_uid=firebase_uid
            )
            coach = crud.create_coach(db, coach=new_coach)
            logger.info(f"Created new coach during login: {firebase_uid}")
        else:
            # Update last login time for existing coach
            crud.update_coach_login_time(db, coach.id)
            logger.info(f"Updated login time for existing coach: {coach.id}")

        return coach
    except Exception as e:
        logger.error(f"Login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )
