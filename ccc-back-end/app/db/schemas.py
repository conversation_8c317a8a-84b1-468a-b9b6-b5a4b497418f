from pydantic import BaseModel, EmailStr, HttpUrl
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any


# Base Schemas
class CoachBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None


class ClientBase(BaseModel):
    cosmos_user_id: uuid.UUID
    cosmos_profile_id: uuid.UUID
    client_full_name: Optional[str] = None


class JobMarketDataBase(BaseModel):
    trend_title: str
    summary: str
    source_url: Optional[HttpUrl] = None
    data_type: str
    region: Optional[str] = "Global"
    industry: Optional[str] = None


class ReportBase(BaseModel):
    report_content: str
    source_session_id: Optional[str] = None


# Agent Schemas
class SubAgent(BaseModel):
    name: str
    status: str


class Agent(BaseModel):
    name: str
    status: str
    sub_agent: List[SubAgent] = []


# Schemas for Creation (what the API receives)
class CoachCreate(CoachBase):
    firebase_uid: str


class ClientCreate(ClientBase):
    pass


class AddClientRequest(BaseModel):
    cosmos_profile_id: uuid.UUID


class JobMarketDataCreate(JobMarketDataBase):
    pass


class ReportCreate(ReportBase):
    client_id: uuid.UUID


# Schemas for Reading (what the API returns)
class Coach(CoachBase):
    id: uuid.UUID
    created_at: datetime
    last_login_at: Optional[datetime] = None
    is_active: bool

    class Config:
        from_attributes = True


class Client(ClientBase):
    id: uuid.UUID
    coach_id: uuid.UUID
    added_at: datetime

    class Config:
        from_attributes = True


class JobMarketData(JobMarketDataBase):
    id: uuid.UUID
    recorded_at: datetime

    class Config:
        from_attributes = True


class Report(ReportBase):
    id: uuid.UUID
    client_id: uuid.UUID
    coach_id: uuid.UUID
    access_token: str
    created_at: datetime
    sent_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Schemas with Relationships
class CoachWithClients(Coach):
    clients: List[Client] = []


class ClientWithReports(Client):
    reports: List[Report] = []


class Token(BaseModel):
    access_token: str
    token_type: str


class LoginResponse(BaseModel):
    access_token: str
    token_type: str
    user: Coach


class TokenData(BaseModel):
    firebase_uid: Optional[str] = None


class DashboardSummary(BaseModel):
    global_talent_pool_count: int
    my_clients_count: int
    active_jobs_count: int
    active_agents: List[Agent]


class ProfileJobMatchRequest(BaseModel):
    profile_keywords: List[str]
    job_preferences: Optional[Dict[str, Any]] = None
    top: int = 20
    skip: int = 0
    boost_recent: bool = True


class StreamRequest(BaseModel):
    message: str
    session_id: str
    client_id: List[uuid.UUID] = []

class MessageRequest(BaseModel):
    user_id: str
    message: str


class InterruptRequest(BaseModel):
    user_id: str
