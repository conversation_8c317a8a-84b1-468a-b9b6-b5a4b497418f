import uuid
from sqlalchemy import (
    <PERSON><PERSON>an,
    Column,
    DateTime,
    ForeignKey,
    String,
    Text,
    func,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, declarative_base

Base = declarative_base()


class Coach(Base):
    __tablename__ = "coaches"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    firebase_uid = Column(String(128), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    full_name = Column(String(255))
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    last_login_at = Column(DateTime(timezone=True))
    is_active = Column(Boolean, default=True, nullable=False)

    clients = relationship(
        "Client", back_populates="coach", cascade="all, delete-orphan"
    )
    reports = relationship(
        "Report", back_populates="coach", cascade="all, delete-orphan"
    )

    def to_dict(self):
        return {
            "id": str(self.id),
            "firebase_uid": self.firebase_uid,
            "email": self.email,
            "full_name": self.full_name,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_login_at": self.last_login_at.isoformat() if self.last_login_at else None,
            "is_active": self.is_active,
        }


class Client(Base):
    __tablename__ = "clients"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    coach_id = Column(UUID(as_uuid=True), ForeignKey("coaches.id"), nullable=False)
    cosmos_user_id = Column(UUID(as_uuid=True), unique=True, nullable=False)
    cosmos_profile_id = Column(UUID(as_uuid=True), unique=True, nullable=False)
    added_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    client_full_name = Column(String(255))

    coach = relationship("Coach", back_populates="clients")
    reports = relationship(
        "Report", back_populates="client", cascade="all, delete-orphan"
    )

    def to_dict(self):
        return {
            "id": str(self.id),
            "coach_id": str(self.coach_id),
            "cosmos_user_id": str(self.cosmos_user_id),
            "cosmos_profile_id": str(self.cosmos_profile_id),
            "added_at": self.added_at.isoformat() if self.added_at else None,
            "client_full_name": self.client_full_name,
        }


class JobMarketData(Base):
    __tablename__ = "job_market_data"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    trend_title = Column(String(255), nullable=False)
    summary = Column(Text, nullable=False)
    source_url = Column(String(512))
    data_type = Column(
        String(50), nullable=False
    )  # e.g., 'TREND', 'SALARY_INFO', 'SKILL_DEMAND'
    region = Column(String(100), nullable=False, default="Global")
    industry = Column(String(100))
    recorded_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )

    def to_dict(self):
        return {
            "id": str(self.id),
            "trend_title": self.trend_title,
            "summary": self.summary,
            "source_url": self.source_url,
            "data_type": self.data_type,
            "region": self.region,
            "industry": self.industry,
            "recorded_at": self.recorded_at.isoformat() if self.recorded_at else None,
        }


class Report(Base):
    __tablename__ = "reports"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    client_id = Column(UUID(as_uuid=True), ForeignKey("clients.id"), nullable=False)
    coach_id = Column(UUID(as_uuid=True), ForeignKey("coaches.id"), nullable=False)
    report_content = Column(Text, nullable=False)
    access_token = Column(String(64), unique=True, nullable=False, index=True)
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    sent_at = Column(DateTime(timezone=True))
    source_session_id = Column(String(255))

    client = relationship("Client", back_populates="reports")
    coach = relationship("Coach", back_populates="reports")

    def to_dict(self):
        return {
            "id": str(self.id),
            "client_id": str(self.client_id),
            "coach_id": str(self.coach_id),
            "report_content": self.report_content,
            "access_token": self.access_token,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "sent_at": self.sent_at.isoformat() if self.sent_at else None,
            "source_session_id": self.source_session_id,
        }
