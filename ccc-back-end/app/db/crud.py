import uuid
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from . import models, schemas
from datetime import UTC, datetime


# Coach CRUD
def get_coach(db: Session, coach_id: uuid.UUID):
    return db.query(models.Coach).filter(models.Coach.id == coach_id).first()


def get_coach_by_email(db: Session, email: str):
    return db.query(models.Coach).filter(models.Coach.email == email).first()


def get_coach_by_firebase_uid(db: Session, firebase_uid: str):
    return (
        db.query(models.Coach).filter(models.Coach.firebase_uid == firebase_uid).first()
    )


def get_coaches(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Coach).offset(skip).limit(limit).all()


def create_coach(db: Session, coach: schemas.CoachCreate):
    db_coach = models.Coach(
        email=coach.email,
        full_name=coach.full_name,
        firebase_uid=coach.firebase_uid,
        last_login_at=datetime.now(UTC),
    )
    db.add(db_coach)
    db.commit()
    db.refresh(db_coach)
    return db_coach


def update_coach_login_time(db: Session, coach_id: uuid.UUID):
    db_coach = get_coach(db, coach_id)
    if db_coach:
        db_coach.last_login_at = datetime.now(UTC)
        db.commit()
        db.refresh(db_coach)
    return db_coach


# Client CRUD
def get_client(db: Session, client_id: uuid.UUID):
    return db.query(models.Client).filter(models.Client.id == client_id).first()


def get_clients_by_coach(
    db: Session, coach_id: uuid.UUID, skip: int = 0, limit: int = 100
):
    return (
        db.query(models.Client)
        .filter(models.Client.coach_id == coach_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def create_client(db: Session, client: schemas.ClientCreate, coach_id: uuid.UUID):
    db_client = models.Client(**client.dict(), coach_id=coach_id)
    db.add(db_client)
    db.commit()
    db.refresh(db_client)
    return db_client


def get_client_by_cosmos_profile_id(
    db: Session, cosmos_profile_id: uuid.UUID, coach_id: uuid.UUID
):
    """Check if a profile is already a client of the coach."""
    return (
        db.query(models.Client)
        .filter(
            models.Client.cosmos_profile_id == cosmos_profile_id,
            models.Client.coach_id == coach_id,
        )
        .first()
    )


def delete_client(db: Session, client_id: uuid.UUID):
    db_client = db.query(models.Client).filter(models.Client.id == client_id).first()
    if db_client:
        db.delete(db_client)
        db.commit()
    return db_client


# Report CRUD
def get_report(db: Session, report_id: uuid.UUID):
    return db.query(models.Report).filter(models.Report.id == report_id).first()


def get_report_by_access_token(db: Session, token: str):
    return db.query(models.Report).filter(models.Report.access_token == token).first()


def get_reports_by_client(
    db: Session, client_id: uuid.UUID, skip: int = 0, limit: int = 100
):
    return (
        db.query(models.Report)
        .filter(models.Report.client_id == client_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def create_report(db: Session, report: schemas.ReportCreate, coach_id: uuid.UUID):
    # Generate a unique access token for the report
    access_token = str(uuid.uuid4()).replace("-", "")
    db_report = models.Report(
        **report.dict(), coach_id=coach_id, access_token=access_token
    )
    db.add(db_report)
    db.commit()
    db.refresh(db_report)
    return db_report


# Job Market Data CRUD
def get_job_market_data(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.JobMarketData).offset(skip).limit(limit).all()


def create_job_market_data(db: Session, data: schemas.JobMarketDataCreate):
    db_data = models.JobMarketData(**data.dict())
    db.add(db_data)
    db.commit()
    db.refresh(db_data)
    return db_data


# Async Client CRUD
async def get_clients_by_coach_async(
    db: AsyncSession, coach_id: uuid.UUID, skip: int = 0, limit: int = 100
):
    """Async version of get_clients_by_coach"""
    result = await db.execute(
        select(models.Client)
        .filter(models.Client.coach_id == coach_id)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()


# Async Job Market Data CRUD
async def get_job_market_data_async(db: AsyncSession, skip: int = 0, limit: int = 100):
    """Async version of get_job_market_data"""
    result = await db.execute(
        select(models.JobMarketData).offset(skip).limit(limit)
    )
    return result.scalars().all()


# Client name search CRUD
async def get_clients_by_name_async(
    db: AsyncSession, coach_id: uuid.UUID, search_name: str, limit: int = 50
):
    """
    Async function to search for clients by partial or full name for a specific coach.
    Uses case-insensitive search and supports partial matching.
    
    Args:
        db: Async database session
        coach_id: The coach's UUID
        search_name: Partial or full name to search for
        limit: Maximum number of clients to return
        
    Returns:
        List of Client objects matching the search criteria
    """
    search_pattern = f"%{search_name.lower()}%"
    result = await db.execute(
        select(models.Client)
        .filter(
            models.Client.coach_id == coach_id,
            models.Client.client_full_name.ilike(search_pattern)
        )
        .limit(limit)
    )
    return result.scalars().all()


def get_clients_by_name(
    db: Session, coach_id: uuid.UUID, search_name: str, limit: int = 50
):
    """
    Synchronous function to search for clients by partial or full name for a specific coach.
    Uses case-insensitive search and supports partial matching.
    
    Args:
        db: Database session
        coach_id: The coach's UUID
        search_name: Partial or full name to search for
        limit: Maximum number of clients to return
        
    Returns:
        List of Client objects matching the search criteria
    """
    search_pattern = f"%{search_name.lower()}%"
    return (
        db.query(models.Client)
        .filter(
            models.Client.coach_id == coach_id,
            models.Client.client_full_name.ilike(search_pattern)
        )
        .limit(limit)
        .all()
    )
