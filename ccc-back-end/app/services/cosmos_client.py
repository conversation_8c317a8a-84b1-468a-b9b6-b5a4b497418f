from azure.cosmos import CosmosClient as AzureCosmosClient
from app.core.config import settings
import logging
from typing import Any

# Configure logger
logger = logging.getLogger(__name__)


class CosmosClient:
    """Base Cosmos DB client for common operations."""
    
    def __init__(self):
        self.client = AzureCosmosClient(
            url=settings.COSMOS_ENDPOINT, credential=settings.COSMOS_KEY
        )
        
        # Get database client first
        self.database = self.client.get_database_client(settings.COSMOS_DATABASE_NAME)
        
        # Initialize containers from database
        print(f"Initializing containers from database: {settings.COSMOS_JOBS_CONTAINER}")
        self.jobs_container = self.database.get_container_client(settings.COSMOS_JOBS_CONTAINER)
        self.profiles_container = self.database.get_container_client(settings.COSMOS_PROFILES_CONTAINER)

    def get_client_profile(self, cosmos_user_id: str):
        """Get a client profile from the profiles container."""
        try:
            query = f"SELECT * FROM c WHERE c.id = '{cosmos_user_id}'"
            items = list(
                self.profiles_container.query_items(query, enable_cross_partition_query=True)
            )
            if items:
                return items[0]
            return None
        except Exception as e:
            logger.error(f"Error retrieving client profile {cosmos_user_id}: {e}")
            return None


class ProfileCosmosService:
    """CRUD operations for Profile model using Cosmos DB.
    
    This service handles profile-related database operations using Azure Cosmos DB.
    Focuses on talent profiles stored in the dots-profile-test container.
    """
    
    def __init__(self):
        self.client = AzureCosmosClient(
            url=settings.COSMOS_ENDPOINT, credential=settings.COSMOS_KEY
        )
        # Get database client first, then container
        self.database = self.client.get_database_client(settings.COSMOS_DATABASE_NAME)
        self.container = self.database.get_container_client(settings.COSMOS_PROFILES_CONTAINER)

    async def get_profile(self, profile_id: str) -> dict[str, Any] | None:
        """Retrieve a specific profile by its ID.

        Args:
            profile_id (str): The unique identifier of the profile

        Returns:
            Optional[Dict[str, Any]]: The profile document if found, None otherwise
        """
        try:
            query = "SELECT * FROM c WHERE c.id = @profile_id"
            parameters = [{"name": "@profile_id", "value": profile_id}]
            
            items = list(
                self.container.query_items(
                    query=query, 
                    parameters=parameters,
                    enable_cross_partition_query=True
                )
            )
            
            return items[0] if items else None
            
        except Exception as e:
            logger.error(f"Error retrieving profile {profile_id}: {e}")
            return None

    async def get_profiles(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        job_search_status: str | None = None,
        preferred_industries: list[str] | None = None,
    ) -> list[dict[str, Any]]:
        """Get profiles with filtering options.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            job_search_status: Filter by job search status
            preferred_industries: Filter by preferred industries
            
        Returns:
            List of profile documents matching the criteria
        """
        try:
            query_parts = []
            parameters = []

            if job_search_status:
                query_parts.append("c.preferences.jobSearchStatus = @job_search_status")
                parameters.append({"name": "@job_search_status", "value": job_search_status})

            if preferred_industries:
                # Check if any of the preferred industries match
                industry_conditions = []
                for i, industry in enumerate(preferred_industries):
                    param_name = f"@industry_{i}"
                    industry_conditions.append(f"ARRAY_CONTAINS(c.preferences.preferredIndustries, {param_name})")
                    parameters.append({"name": param_name, "value": industry})
                
                if industry_conditions:
                    query_parts.append(f"({' OR '.join(industry_conditions)})")

            # Build the complete query
            if query_parts:
                where_clause = " WHERE " + " AND ".join(query_parts)
            else:
                where_clause = ""

            query = f"SELECT * FROM c{where_clause} ORDER BY c.lastUpdatedAt DESC OFFSET {skip} LIMIT {limit}"

            items = list(
                self.container.query_items(
                    query=query,
                    parameters=parameters,
                    enable_cross_partition_query=True
                )
            )

            return items

        except Exception as e:
            logger.error(f"Error retrieving profiles: {e}")
            return []

    async def count_profiles(
        self,
        *,
        job_search_status: str | None = None,
        preferred_industries: list[str] | None = None,
    ) -> int:
        """Count profiles with filtering options.
        
        Args:
            job_search_status: Filter by job search status
            preferred_industries: Filter by preferred industries
            
        Returns:
            Number of profiles matching the criteria
        """
        try:
            query_parts = []
            parameters = []

            if job_search_status:
                query_parts.append("c.preferences.jobSearchStatus = @job_search_status")
                parameters.append({"name": "@job_search_status", "value": job_search_status})

            if preferred_industries:
                industry_conditions = []
                for i, industry in enumerate(preferred_industries):
                    param_name = f"@industry_{i}"
                    industry_conditions.append(f"ARRAY_CONTAINS(c.preferences.preferredIndustries, {param_name})")
                    parameters.append({"name": param_name, "value": industry})
                
                if industry_conditions:
                    query_parts.append(f"({' OR '.join(industry_conditions)})")

            # Build the complete query
            if query_parts:
                where_clause = " WHERE " + " AND ".join(query_parts)
            else:
                where_clause = ""

            query = f"SELECT VALUE COUNT(1) FROM c{where_clause}"

            items = list(
                self.container.query_items(
                    query=query,
                    parameters=parameters,
                    enable_cross_partition_query=True
                )
            )

            return items[0] if items else 0

        except Exception as e:
            logger.error(f"Error counting profiles: {e}")
            return 0


class JobCosmosService:
    """CRUD operations for Job model using Cosmos DB.
    
    This service handles job-related database operations using Azure Cosmos DB.
    Currently focused on reading job records with various filtering options.
    """
    
    def __init__(self):
        self.client = AzureCosmosClient(
            url=settings.COSMOS_ENDPOINT, credential=settings.COSMOS_KEY
        )
        # Get database client first, then container
        self.database = self.client.get_database_client(settings.COSMOS_DATABASE_NAME)
        self.container = self.database.get_container_client(settings.COSMOS_JOBS_CONTAINER)

    async def get_job(self, job_id: str, company_id: str) -> dict[str, Any] | None:
        """Retrieve a specific job by its ID and company ID.

        Args:
            job_id (str): The unique identifier of the job
            company_id (str): The company ID that owns the job

        Returns:
            Optional[Dict[str, Any]]: The job document if found, None otherwise
        """
        try:
            # Use direct read for better performance when we have both id and partition key
            try:
                job = self.container.read_item(
                    item=job_id,
                    partition_key=company_id
                )
                # Ensure organization_info is always a valid dictionary
                if job.get("organization_info") is None:
                    job["organization_info"] = {}
                return job
            except Exception as read_error:
                logger.warning(f"Direct read failed for job {job_id}, falling back to query: {read_error}")
                
                # Fallback to query if direct read fails
                query = "SELECT * FROM c WHERE c.id = @job_id AND c.company_id = @company_id"
                parameters = [
                    {"name": "@job_id", "value": job_id},
                    {"name": "@company_id", "value": company_id}
                ]
                
                items = list(
                    self.container.query_items(
                        query=query, 
                        parameters=parameters,
                        partition_key=company_id,
                        enable_cross_partition_query=False
                    )
                )
                
                if items:
                    job = items[0]
                    # Ensure organization_info is always a valid dictionary
                    if job.get("organization_info") is None:
                        job["organization_info"] = {}
                    return job
                return None
            
        except Exception as e:
            logger.error(f"Error retrieving job {job_id}: {e}")
            return None

    async def get_jobs(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        company_id: str | None = None,
        creator_id: str | None = None,
        status: str | None = None,
        job_type: str | None = None,
        active_only: bool = False,
    ) -> list[dict[str, Any]]:
        """Get jobs with filtering options.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            company_id: Filter by company ID
            creator_id: Filter by creator ID
            status: Filter by job status
            job_type: Filter by job type
            active_only: Filter for active jobs only
            
        Returns:
            List of job documents matching the criteria
        """
        try:
            # Build query parts based on filters
            query_parts = []
            parameters = []

            if company_id:
                query_parts.append("c.company_id = @company_id")
                parameters.append({"name": "@company_id", "value": company_id})

            if creator_id:
                query_parts.append("c.creator_id = @creator_id")
                parameters.append({"name": "@creator_id", "value": creator_id})

            if status:
                query_parts.append("c.status = @status")
                parameters.append({"name": "@status", "value": status})

            if job_type:
                query_parts.append("c.job_type = @job_type")
                parameters.append({"name": "@job_type", "value": job_type})

            if active_only:
                query_parts.append("c.status = 'active'")
                query_parts.append("(NOT IS_DEFINED(c.expires_at) OR c.expires_at > GetCurrentDateTime())")

            # Build the complete query
            if query_parts:
                where_clause = " WHERE " + " AND ".join(query_parts)
            else:
                where_clause = ""

            # Use _ts (timestamp) for ordering since it's more reliable than created_at for cross-partition queries
            query = f"SELECT * FROM c{where_clause} ORDER BY c._ts DESC OFFSET {skip} LIMIT {limit}"
            
            # Log query for debugging
            logger.info(f"Executing jobs query: {query} with parameters: {parameters}")

            # Configure query options
            query_options = {
                "enable_cross_partition_query": True,
                "max_item_count": limit,
                "max_degree_of_parallelism": -1,  # Enable parallel execution
            }
            
            # If company_id is specified, we can use partition key for better performance
            if company_id:
                query_options["partition_key"] = company_id
                query_options["enable_cross_partition_query"] = False
                logger.info(f"Using partition key: {company_id}")
            else:
                logger.info("No company_id specified, enabling cross-partition query")

            items = list(
                self.container.query_items(
                    query=query,
                    parameters=parameters,
                    **query_options
                )
            )
            
            logger.info(f"Retrieved {len(items)} jobs from Cosmos DB")

            # Ensure organization_info is always a valid dictionary for each job
            for job in items:
                if job.get("organization_info") is None:
                    job["organization_info"] = {}

            return items

        except Exception as e:
            logger.error(f"Error retrieving jobs: {e}")
            # If cross-partition query fails, try a simpler approach
            try:
                logger.info("Attempting fallback query without ordering")
                fallback_query = f"SELECT * FROM c{where_clause}"
                items = list(
                    self.container.query_items(
                        query=fallback_query,
                        parameters=parameters,
                        enable_cross_partition_query=True,
                        max_item_count=limit
                    )
                )
                # Sort in Python if needed
                items = sorted(items, key=lambda x: x.get('_ts', 0), reverse=True)
                return items[skip:skip+limit]
            except Exception as fallback_error:
                logger.error(f"Fallback query also failed: {fallback_error}")
                return []

    async def search_jobs(
        self,
        *,
        query: str | None = None,
        company_id: str | None = None,
        status: str | None = "active",
        job_type: str | None = None,
        skip: int = 0,
        limit: int = 20,
    ) -> list[dict[str, Any]]:
        """Search for jobs using various criteria.

        Args:
            query: Search text for job title/description
            company_id: Filter by company ID
            status: Filter by job status
            job_type: Filter by job type
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List of job documents matching the search criteria
        """
        try:
            # Build query parts based on filters
            query_parts = []
            parameters = []

            if company_id:
                query_parts.append("c.company_id = @company_id")
                parameters.append({"name": "@company_id", "value": company_id})

            if status:
                query_parts.append("c.status = @status")
                parameters.append({"name": "@status", "value": status})

            if job_type:
                query_parts.append("c.job_type = @job_type")
                parameters.append({"name": "@job_type", "value": job_type})

            if query:
                # Simple text search in title and description
                query_parts.append("(CONTAINS(LOWER(c.title), LOWER(@search_query)) OR CONTAINS(LOWER(c.job_description), LOWER(@search_query)))")
                parameters.append({"name": "@search_query", "value": query})

            # Build the complete query
            if query_parts:
                where_clause = " WHERE " + " AND ".join(query_parts)
            else:
                where_clause = ""

            # Use _ts for ordering for better cross-partition performance
            sql_query = f"SELECT * FROM c{where_clause} ORDER BY c._ts DESC OFFSET {skip} LIMIT {limit}"
            
            # Log query for debugging
            logger.info(f"Executing job search query: {sql_query} with parameters: {parameters}")

            # Configure query options
            query_options = {
                "max_item_count": limit,
                "max_degree_of_parallelism": -1,
            }

            # If company_id is specified, use partition key for better performance
            if company_id:
                query_options["partition_key"] = company_id
                query_options["enable_cross_partition_query"] = False
                logger.info(f"Using partition key for search: {company_id}")
            else:
                query_options["enable_cross_partition_query"] = True
                logger.info("Using cross-partition query for search")

            items = list(
                self.container.query_items(
                    query=sql_query,
                    parameters=parameters,
                    **query_options
                )
            )
            
            logger.info(f"Retrieved {len(items)} jobs from search")

            # Ensure organization_info is always a valid dictionary for each job
            for job in items:
                if job.get("organization_info") is None:
                    job["organization_info"] = {}

            return items

        except Exception as e:
            logger.error(f"Error searching jobs: {e}")
            # Fallback to simpler query
            try:
                logger.info("Attempting fallback search query")
                fallback_query = f"SELECT * FROM c{where_clause}"
                items = list(
                    self.container.query_items(
                        query=fallback_query,
                        parameters=parameters,
                        enable_cross_partition_query=True,
                        max_item_count=limit
                    )
                )
                # Sort in Python
                items = sorted(items, key=lambda x: x.get('_ts', 0), reverse=True)
                return items[skip:skip+limit]
            except Exception as fallback_error:
                logger.error(f"Fallback search query failed: {fallback_error}")
                return []

    async def count_jobs(
        self,
        *,
        company_id: str | None = None,
        creator_id: str | None = None,
        status: str | None = None,
        job_type: str | None = None,
        active_only: bool = False,
    ) -> int:
        """Count jobs with filtering options.
        
        Args:
            company_id: Filter by company ID
            creator_id: Filter by creator ID
            status: Filter by job status
            job_type: Filter by job type
            active_only: Filter for active jobs only
            
        Returns:
            Number of jobs matching the criteria
        """
        try:
            # Build query parts based on filters
            query_parts = []
            parameters = []

            if company_id:
                query_parts.append("c.company_id = @company_id")
                parameters.append({"name": "@company_id", "value": company_id})

            if creator_id:
                query_parts.append("c.creator_id = @creator_id")
                parameters.append({"name": "@creator_id", "value": creator_id})

            if status:
                query_parts.append("c.status = @status")
                parameters.append({"name": "@status", "value": status})

            if job_type:
                query_parts.append("c.job_type = @job_type")
                parameters.append({"name": "@job_type", "value": job_type})

            if active_only:
                query_parts.append("c.status = 'active'")
                query_parts.append("(NOT IS_DEFINED(c.expires_at) OR c.expires_at > GetCurrentDateTime())")

            # Build the complete query
            if query_parts:
                where_clause = " WHERE " + " AND ".join(query_parts)
            else:
                where_clause = ""

            query = f"SELECT VALUE COUNT(1) FROM c{where_clause}"
            
            # Log query for debugging
            logger.info(f"Executing job count query: {query} with parameters: {parameters}")

            # Configure query options
            query_options = {
                "max_degree_of_parallelism": -1,
            }

            # If company_id is specified, use partition key for better performance
            if company_id:
                query_options["partition_key"] = company_id
                query_options["enable_cross_partition_query"] = False
                logger.info(f"Using partition key for count: {company_id}")
            else:
                query_options["enable_cross_partition_query"] = True
                logger.info("Using cross-partition query for count")

            items = list(
                self.container.query_items(
                    query=query,
                    parameters=parameters,
                    **query_options
                )
            )
            
            count = items[0] if items else 0
            logger.info(f"Job count result: {count}")
            return count

        except Exception as e:
            logger.error(f"Error counting jobs: {e}")
            # Fallback to simple count
            try:
                logger.info("Attempting fallback count query")
                fallback_query = f"SELECT VALUE COUNT(1) FROM c{where_clause}"
                items = list(
                    self.container.query_items(
                        query=fallback_query,
                        parameters=parameters,
                        enable_cross_partition_query=True
                    )
                )
                count = items[0] if items else 0
                logger.info(f"Fallback count result: {count}")
                return count
            except Exception as fallback_error:
                logger.error(f"Fallback count query failed: {fallback_error}")
                return 0


# Initialize service instances
cosmos_client = CosmosClient()
job_cosmos_service = JobCosmosService()
profile_cosmos_service = ProfileCosmosService()


def get_cosmos_container():
    """Returns the Cosmos DB jobs container client instance."""
    return cosmos_client.jobs_container


def get_cosmos_profiles_container():
    """Returns the Cosmos DB profiles container client instance."""
    return cosmos_client.profiles_container


def get_job_cosmos_service():
    """Returns the Job Cosmos service instance."""
    return job_cosmos_service


def get_profile_cosmos_service():
    """Returns the Profile Cosmos service instance."""
    return profile_cosmos_service
