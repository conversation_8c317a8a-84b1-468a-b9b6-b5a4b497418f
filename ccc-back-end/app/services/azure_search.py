import logging
import json
from typing import Any, Dict, List, Optional
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential
from azure.search.documents.models import VectorizedQuery
from app.core.config import settings

# Configure logger
logger = logging.getLogger(__name__)


class AzureSearchService:
    """
    Azure Cognitive Search service for job search functionality.
    
    This service provides semantic and vector search capabilities for matching
    job profiles with available positions using Azure AI Search.
    """
    
    def __init__(self):
        """Initialize Azure Search client with configured credentials."""
        try:
            self.search_client = SearchClient(
                endpoint=settings.AZURE_AI_SEARCH_ENDPOINT,
                index_name=settings.AZURE_AI_SEARCH_INDEX_NAME,
                credential=AzureKeyCredential(settings.AZURE_AI_SEARCH_API_KEY)
            )
            logger.info(f"Azure Search client initialized for index: {settings.AZURE_AI_SEARCH_INDEX_NAME}")
        except Exception as e:
            logger.error(f"Failed to initialize Azure Search client: {e}")
            raise

    async def search_jobs_by_query(
        self,
        query: str,
        *,
        top: int = 20,
        skip: int = 0,
        filters: Optional[str] = None,
        order_by: Optional[List[str]] = None,
        select: Optional[List[str]] = None,
        search_mode: str = "any",
        semantic_ranking: bool = True
    ) -> Dict[str, Any]:
        """
        Search for jobs using a text query with Azure Search.
        
        Args:
            query: Search query string (keywords, skills, job titles, etc.)
            top: Number of results to return (default: 20)
            skip: Number of results to skip for pagination (default: 0)
            filters: OData filter expression for additional filtering
            order_by: List of fields to order results by
            select: List of fields to include in results
            search_mode: Search mode ('any' or 'all')
            semantic_ranking: Whether to use semantic ranking for better relevance
            
        Returns:
            Dictionary containing search results and metadata
        """
        try:
            # Configure search parameters
            search_params = {
                "search_text": query,
                "top": top,
                "skip": skip,
                "search_mode": search_mode,
                "include_total_count": True,
                "scoring_statistics": "global"
            }
            
            # Add optional parameters if provided
            if filters:
                search_params["filter"] = filters
            if order_by:
                search_params["order_by"] = order_by
            if select:
                search_params["select"] = select
                
            # Enable semantic search if available
            if semantic_ranking:
                search_params["query_type"] = "semantic"
                search_params["semantic_configuration_name"] = "default"
                search_params["query_caption"] = "extractive"
                search_params["query_answer"] = "extractive"
            
            logger.info(f"Executing Azure Search query: '{query}' with params: {search_params}")
            
            # Execute search
            results = self.search_client.search(**search_params)
            
            # Process results
            jobs = []
            for result in results:
                job = dict(result)
                
                # Add search score and semantic information if available
                if hasattr(result, '@search.score'):
                    job['search_score'] = getattr(result, '@search.score')
                if hasattr(result, '@search.captions'):
                    job['search_captions'] = getattr(result, '@search.captions')
                if hasattr(result, '@search.answers'):
                    job['search_answers'] = getattr(result, '@search.answers')
                    
                jobs.append(job)
            
            # Get total count if available
            total_count = getattr(results, 'get_count', lambda: None)() or len(jobs)
            
            search_results = {
                "jobs": jobs,
                "total_count": total_count,
                "query": query,
                "returned_count": len(jobs),
                "skip": skip,
                "top": top
            }
            
            logger.info(f"Azure Search returned {len(jobs)} jobs out of {total_count} total matches")
            return search_results
            
        except Exception as e:
            logger.error(f"Error executing Azure Search query '{query}': {e}")
            return {
                "jobs": [],
                "total_count": 0,
                "query": query,
                "error": f"Search failed: {str(e)}",
                "returned_count": 0,
                "skip": skip,
                "top": top
            }

    async def find_matching_jobs_by_profile(
        self,
        profile_keywords: List[str],
        *,
        job_preferences: Optional[Dict[str, Any]] = None,
        top: int = 20,
        skip: int = 0,
        boost_recent: bool = True
    ) -> Dict[str, Any]:
        """
        Find matching jobs for a profile based on keywords and preferences.
        
        Args:
            profile_keywords: List of keywords/skills from the profile
            job_preferences: Optional job preferences (location, salary, etc.)
            top: Number of results to return (default: 20)
            skip: Number of results to skip for pagination (default: 0)
            boost_recent: Whether to boost recent job postings (default: True)
            
        Returns:
            Dictionary containing matching jobs and metadata
        """
        try:
            # Build search query from profile keywords
            query = " OR ".join([f'"{keyword}"' for keyword in profile_keywords])
            
            # Build filters based on job preferences
            filters = []
            
            # Always filter for active jobs
            filters.append("status eq 'active'")
            
            # Add preference-based filters
            if job_preferences:
                if job_preferences.get("preferred_location"):
                    location = job_preferences["preferred_location"]
                    filters.append(f"search.ismatch('{location}', 'location')")
                
                if job_preferences.get("min_salary"):
                    min_salary = job_preferences["min_salary"]
                    filters.append(f"salary_min ge {min_salary}")
                
                if job_preferences.get("max_salary"):
                    max_salary = job_preferences["max_salary"]
                    filters.append(f"salary_max le {max_salary}")
                
                if job_preferences.get("job_type"):
                    job_type = job_preferences["job_type"]
                    filters.append(f"job_type eq '{job_type}'")
                
                if job_preferences.get("remote_work"):
                    if job_preferences["remote_work"]:
                        filters.append("(work_arrangement eq 'remote' or work_arrangement eq 'hybrid')")
                
                if job_preferences.get("preferred_industries"):
                    industries = job_preferences["preferred_industries"]
                    industry_filter = " or ".join([f"industry eq '{industry}'" for industry in industries])
                    filters.append(f"({industry_filter})")
            
            # Combine filters
            filter_expression = " and ".join(filters) if filters else None
            
            # Configure ordering - boost recent jobs if requested
            order_by = ["search.score() desc"]
            if boost_recent:
                order_by.insert(0, "created_at desc")
            
            # Execute search with profile-specific parameters
            search_results = await self.search_jobs_by_query(
                query=query,
                top=top,
                skip=skip,
                filters=filter_expression,
                order_by=order_by,
                semantic_ranking=True
            )
            
            # Add profile matching metadata
            search_results["profile_keywords"] = profile_keywords
            search_results["job_preferences"] = job_preferences
            search_results["match_type"] = "profile_based"
            
            logger.info(f"Profile-based search for {len(profile_keywords)} keywords returned {len(search_results['jobs'])} jobs")
            return search_results
            
        except Exception as e:
            logger.error(f"Error in profile-based job search: {e}")
            return {
                "jobs": [],
                "total_count": 0,
                "profile_keywords": profile_keywords,
                "job_preferences": job_preferences,
                "error": f"Profile-based search failed: {str(e)}",
                "returned_count": 0,
                "skip": skip,
                "top": top,
                "match_type": "profile_based"
            }

    async def suggest_jobs(
        self,
        partial_query: str,
        *,
        suggester_name: str = "job-suggester",
        top: int = 10
    ) -> Dict[str, Any]:
        """
        Get job suggestions for autocomplete functionality.
        
        Args:
            partial_query: Partial search query for suggestions
            suggester_name: Name of the suggester to use
            top: Number of suggestions to return
            
        Returns:
            Dictionary containing suggestions
        """
        try:
            # Note: This requires a suggester to be configured in the Azure Search index
            # For now, we'll implement a simple prefix search
            search_results = await self.search_jobs_by_query(
                query=f"{partial_query}*",
                top=top,
                select=["id", "title", "company_name", "location"],
                semantic_ranking=False
            )
            
            # Extract suggestions from search results
            suggestions = []
            for job in search_results["jobs"]:
                suggestion = {
                    "text": job.get("title", ""),
                    "company": job.get("company_name", ""),
                    "location": job.get("location", ""),
                    "job_id": job.get("id", "")
                }
                suggestions.append(suggestion)
            
            return {
                "suggestions": suggestions,
                "query": partial_query,
                "count": len(suggestions)
            }
            
        except Exception as e:
            logger.error(f"Error getting job suggestions for '{partial_query}': {e}")
            return {
                "suggestions": [],
                "query": partial_query,
                "error": f"Suggestions failed: {str(e)}",
                "count": 0
            }


# Global service instance
azure_search_service = AzureSearchService()


# Dependency function for FastAPI
def get_azure_search_service() -> AzureSearchService:
    """Dependency function to get Azure Search service instance."""
    return azure_search_service 