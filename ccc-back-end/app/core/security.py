import firebase_admin
from firebase_admin import auth, credentials
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JW<PERSON>rror, jwt
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from app.core.config import settings
from app.db import crud, models
from app.db.session import get_db
import logging

# Configure logger
logger = logging.getLogger(__name__)

# Initialize Firebase Admin SDK
cred = credentials.ApplicationDefault()
firebase_admin.initialize_app(
    cred,
    {
        "projectId": settings.FIREBASE_PROJECT_ID,
    },
)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")


def create_access_token(data: dict, expires_delta: timedelta | None = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def verify_firebase_token(token: str) -> dict:
    try:
        decoded_token = auth.verify_id_token(token)
        return decoded_token
    except Exception as e:
        logger.error(f"Firebase token verification failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid or expired Firebase token: {e}",
            headers={"WWW-Authenticate": "Bearer"},
        )


def get_current_user(
    token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)
) -> models.Coach:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Directly verify Firebase token
        firebase_user = verify_firebase_token(token)
        firebase_uid = firebase_user["uid"]
        
        # Get or create coach
        coach = crud.get_coach_by_firebase_uid(db, firebase_uid=firebase_uid)
        if not coach:
            # Create a new coach if one doesn't exist
            user_email = firebase_user.get("email")
            user_name = firebase_user.get("name")
            from app.db.schemas import CoachCreate
            new_coach = CoachCreate(
                email=user_email, full_name=user_name, firebase_uid=firebase_uid
            )
            coach = crud.create_coach(db, coach=new_coach)
            logger.info(f"Created new coach for Firebase UID: {firebase_uid}")
        else:
            # Update last login time for existing coach
            crud.update_coach_login_time(db, coach.id)
            logger.info(f"Updated login time for coach: {coach.id}")
        
        return coach
    except HTTPException as e:
        logger.error(f"Authentication failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected authentication error: {e}")
        raise credentials_exception


def get_current_user_ws(token: str, db: Session) -> models.Coach:
    """
    WebSocket-specific user authentication that uses the same validation logic
    as API endpoints but handles the token directly without dependency injection.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Directly verify Firebase token (same logic as get_current_user)
        firebase_user = verify_firebase_token(token)
        firebase_uid = firebase_user["uid"]
        
        # Get or create coach (same logic as get_current_user)
        coach = crud.get_coach_by_firebase_uid(db, firebase_uid=firebase_uid)
        if not coach:
            # Create a new coach if one doesn't exist
            user_email = firebase_user.get("email")
            user_name = firebase_user.get("name")
            from app.db.schemas import CoachCreate
            new_coach = CoachCreate(
                email=user_email, full_name=user_name, firebase_uid=firebase_uid
            )
            coach = crud.create_coach(db, coach=new_coach)
            logger.info(f"Created new coach for Firebase UID: {firebase_uid}")
        else:
            # Update last login time for existing coach
            crud.update_coach_login_time(db, coach.id)
            logger.info(f"Updated login time for coach: {coach.id}")
        
        return coach
    except HTTPException as e:
        logger.error(f"WebSocket authentication failed: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected WebSocket authentication error: {e}")
        raise credentials_exception
