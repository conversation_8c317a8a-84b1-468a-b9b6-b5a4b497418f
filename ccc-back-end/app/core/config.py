from pydantic_settings import BaseSettings
from typing import List


class Settings(BaseSettings):
    # Environment Configuration
    ENVIRONMENT: str = "production"  # Can be "development" or "production"
    
    # Cosmos DB - Common settings
    COSMOS_ENDPOINT: str
    COSMOS_KEY: str
    COSMOS_DATABASE_NAME: str
    # Cosmos DB Containers
    COSMOS_JOBS_CONTAINER: str 
    COSMOS_PROFILES_CONTAINER: str 
    
    # Postgres DB
    POSTGRES_URI: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str
    
    # Firebase
    FIREBASE_PROJECT_ID: str
    FIREBASE_CREDENTIALS: str
    
    # JWT
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    
    # CORS Configuration
    BACKEND_CORS_ORIGINS: List[str] = ["*"]
    
    # Azure Search
    AZURE_AI_SEARCH_API_KEY: str
    AZURE_AI_SEARCH_ENDPOINT: str
    AZURE_AI_SEARCH_INDEX_NAME: str
    
    # Gemini
    GEMINI_FLASH_MODEL: str = "gemini-2.5-flash"
    GEMINI_FLASH_LITE_MODEL: str = "gemini-2.5-flash-lite-preview-06-17"
    GEMINI_PRO_THINKING_MODEL: str = "gemini-2.5-pro"
    GOOGLE_API_KEY: str
    GOOGLE_GENAI_USE_VERTEXAI: bool = False
    
    @property
    def POSTGRES_DSN(self) -> str:
        """Construct PostgreSQL DSN for psycopg2 (sync) driver with conditional SSL support"""
        base_dsn = f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_URI}/{self.POSTGRES_DB}"
        
        # Add SSL requirement only in production for psycopg2
        if self.ENVIRONMENT.lower() == "production":
            return f"{base_dsn}?sslmode=require"
        else:
            # Development mode - no SSL required
            return base_dsn
    
    @property
    def POSTGRES_ASYNC_DSN(self) -> str:
        """Construct PostgreSQL DSN for asyncpg (async) driver with conditional SSL support"""
        base_dsn = f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_URI}/{self.POSTGRES_DB}"
        
        # Add SSL requirement only in production for asyncpg
        if self.ENVIRONMENT.lower() == "production":
            # asyncpg uses 'ssl' parameter instead of 'sslmode'
            return f"{base_dsn}?ssl=require"
        else:
            # Development mode - no SSL required
            return base_dsn
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode"""
        return self.ENVIRONMENT.lower() == "development"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode"""
        return self.ENVIRONMENT.lower() == "production"
    
    class Config:
        env_file = ".env"


settings = Settings()
