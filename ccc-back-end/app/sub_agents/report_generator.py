import google.adk.agents as agents
from google.adk.tools.function_tool import FunctionTool
from google.adk.tools.tool_context import ToolContext
from app.tools.cosmos_tools import get_client_profile_from_cosmos_tool
from app.tools.db_tools import get_client_detail_by_name_tool
from app.sub_agents.market import job_market_analyst_agent
from app.core.config import settings
from app.db.session import <PERSON><PERSON><PERSON><PERSON>
from app.db import crud, models
from app.db.schemas import ReportCreate
from app.services.cosmos_client import cosmos_client
from app.tools.state_tools import get_selected_client_from_state_tool
import json
import uuid


def read_market_insights_tool(tool_context: ToolContext):
    """
    Reads market insights from tool context. If not available, returns message 
    requesting agent to use sub-agent for generation.
    
    Returns:
        JSON string containing market insights or message to generate them
    """
    market_insights = tool_context.state.get("market_insights")
    if market_insights:
        return json.dumps({
            "status": "success",
            "market_insights": market_insights
        })
    else:
        return json.dumps({
            "status": "missing",
            "message": "Market insights not found in context. Please use return the parent, tell the parent to use the job market analyst sub-agent to generate market insights first and retry the generation."
        })


def read_user_profile_tool(tool_context: ToolContext, candidate_id: str):
    """
    Reads user profile by first checking tool context for selected_candidate,
    then checking parameter. The ID can be either client_id or cosmos_user_id.
    Finds client in database and fetches profile from Cosmos DB.
    
    Args:
        candidate_id: Optional candidate ID (client_id or cosmos_user_id)
        
    Returns:
        JSON string containing user profile or error message
    """
    # First check tool context for selected_candidate
    selected_candidate_id = tool_context.state.get("selected_candidate")
    
    # If not in context, check parameter
    if not selected_candidate_id:
        selected_candidate_id = candidate_id
    
    # If still no ID, return error
    if not selected_candidate_id:
        return json.dumps({
            "status": "error",
            "message": "No candidate ID found. Please provide candidate_id parameter or ensure selected_candidate is set in context."
        })
    
    try:
        # Convert to UUID for database query
        candidate_uuid = uuid.UUID(selected_candidate_id)
        
        db = SessionLocal()
        try:
            # Query client by either client ID or cosmos_user_id
            client = db.query(models.Client).filter(
                (models.Client.id == candidate_uuid) | 
                (models.Client.cosmos_user_id == candidate_uuid)
            ).first()
            
            if not client:
                return json.dumps({
                    "status": "not_found",
                    "message": f"No client found with ID {selected_candidate_id}"
                })
            
            # Store client info in context
            tool_context.state["selected_client"] = client.to_dict()
            
            print(f"client.cosmos_profile_id: {client.cosmos_profile_id}")
            print(f"client.cosmos_user_id: {client.cosmos_user_id}")
            # Fetch profile from Cosmos DB using cosmos_user_id
            profile = cosmos_client.get_client_profile(str(client.cosmos_profile_id))
            
            if not profile:
                return json.dumps({
                    "status": "error",
                    "message": f"Profile not found in Cosmos DB for user {client.cosmos_user_id}"
                })
            
            # Store profile in context
            tool_context.state["client_profile"] = profile
            
            return json.dumps({
                "status": "success",
                "client": client.to_dict(),
                "profile": profile
            })
            
        finally:
            db.close()
            
    except ValueError as e:
        return json.dumps({
            "status": "error",
            "message": f"Invalid UUID format: {str(e)}"
        })
    except Exception as e:
        return json.dumps({
            "status": "error",
            "message": f"Error retrieving profile: {str(e)}"
        })


def generate_and_save_report_tool(
    tool_context: ToolContext,
    report_content: str
):
    """
    Generates and saves a report to the database using context information.
    
    Args:
        tool_context: The tool context containing state information
        report_content: The content of the report to save
        
    Returns:
        JSON string containing the created report information or error message
    """
    try:
        # Get client info from context
        selected_client = tool_context.state.get("selected_client")
        if not selected_client:
            return json.dumps({
                "status": "error",
                "message": "No selected client found in context. Please read user profile first."
            })
        
        # Parse client data if it's a JSON string
        if isinstance(selected_client, str):
            selected_client = json.loads(selected_client)
        
        client_id = selected_client.get("id")
        coach_id = tool_context.state.get("coach_id")
        
        # Convert string UUIDs to UUID objects
        client_uuid = uuid.UUID(client_id)
        coach_uuid = uuid.UUID(coach_id)
        
        db = SessionLocal()
        try:
            # Create report using CRUD function
            report_data = ReportCreate(
                client_id=client_uuid,
                report_content=report_content
            )
            report = crud.create_report(db=db, report=report_data, coach_id=coach_uuid)
            
            # Store report info in context
            tool_context.state["last_created_report"] = report.to_dict()
            
            return json.dumps({
                "status": "success",
                "report": report.to_dict(),
                "access_url": f"/reports/{report.access_token}"
            })
            
        finally:
            db.close()
            
    except ValueError as e:
        return json.dumps({
            "status": "error",
            "message": f"Invalid UUID format: {str(e)}"
        })
    except Exception as e:
        return json.dumps({
            "status": "error",
            "message": f"Failed to create report: {str(e)}"
        })


# Create function tools for the streamlined report generator agent
read_market_insights_function_tool = FunctionTool(
    func=read_market_insights_tool,
)

read_user_profile_function_tool = FunctionTool(
    func=read_user_profile_tool,
)

generate_and_save_report_function_tool = FunctionTool(
    func=generate_and_save_report_tool,
)


# Streamlined instruction prompt for the Report Generator Agent
REPORT_GENERATOR_INSTRUCTIONS = """
You are a specialized Report Generator Agent that creates comprehensive career coaching reports by combining market insights with client-specific analysis.

# Your Primary Mission
Generate personalized career coaching reports that combine market intelligence with individual client profiles to provide actionable career guidance and strategic recommendations.

# Your Streamlined Tools

## 1. read_market_insights_tool
- Reads existing market insights from tool context
- If no insights exist, returns a message requesting you to use the job market analyst sub-agent to generate them
- Always call this first to ensure you have market data

## 2. read_user_profile_tool(candidate_id: str = None)
- Reads user profile by checking tool context for "selected_candidate" first
- If not found in context, uses the candidate_id parameter
- The ID can be either a client_id or cosmos_user_id
- Automatically finds the client in database and fetches full profile from Cosmos DB
- Stores both client and profile data in context for future use

## 3. generate_and_save_report_tool(report_content: str)
- Creates and saves the final report to the database
- Uses client information from context (set by read_user_profile_tool)
- Returns report details including access token and URL

## 4. get_client_detail_by_name_tool(client_name: str)
- Retrieves client details by name from the database
- Useful for finding client IDs or other details when only the client's name is known

# Your Workflow Process

## Step 1: Ensure Market Intelligence
1. Call `read_market_insights_tool()` to check for existing market insights
2. If none exist, use the job market analyst sub-agent to generate them
3. Proceed only when you have market insights available

## Step 2: Get Client Profile
1. Call `read_user_profile_tool(candidate_id)` with the candidate ID if provided
2. The tool will automatically handle finding the client and fetching their full profile
3. Verify you have both client and profile data before proceeding

## Step 3: Generate Comprehensive Report
Create a detailed report using this structure:

```markdown
# Career Coaching Report
*Generated: [Current Date]*
*Client: [Client Name from Profile]*

## Executive Summary
[2-3 sentence overview of key recommendations based on market insights and client profile]

## Client Profile Analysis
### Current Position
- **Name**: [Full name from profile]
- **Current Role**: [From work experience]
- **Industry**: [Current/Target Industry]
- **Experience Level**: [Years/Level]
- **Job Search Status**: [From preferences]

### Key Strengths
- [List top skills with proficiency levels]
- [Relevant experience highlights]
- [Educational background]

### Career Objectives
- [Career goals from profile]
- [Preferred industries]
- [Job search preferences]

## Market Intelligence Insights
### Relevant Market Trends
- [Market trends relevant to client's industry/role]
- [Opportunities aligned with client's experience]
- [Emerging roles that match client profile]

### Skill Demand Analysis
- **High-Demand Skills Client Possesses**: [Client skills that are market-relevant]
- **Skill Gaps to Address**: [Market demands vs client skills]
- **Emerging Technologies**: [New skills worth developing]

## Strategic Recommendations

### Immediate Actions (Next 30 Days)
1. [Specific recommendation based on profile and market]
2. [Skills to prioritize immediately]
3. [Network/industry connections to pursue]

### Medium-Term Goals (3-6 Months)
1. [Skill development plan aligned with market demand]
2. [Industry positioning strategy]
3. [Experience building recommendations]

### Long-Term Strategy (6-12 Months)
1. [Career advancement pathway]
2. [Industry transition strategy if applicable]
3. [Leadership/expertise development plan]

## Market Positioning Strategy
### Competitive Advantages
- [Client's unique strengths in current market context]
- [Differentiating factors from market analysis]

### Positioning Recommendations
- [How to present skills and experience]
- [Industry narrative to develop]
- [Personal branding suggestions]

## Action Plan
### Priority 1: [Most Critical Area]
- **Objective**: [Clear, measurable goal]
- **Actions**: [Specific, actionable steps]
- **Timeline**: [Realistic timeframe]
- **Success Metrics**: [How to measure progress]

### Priority 2: [Secondary Focus]
- **Objective**: [Clear, measurable goal]
- **Actions**: [Specific, actionable steps]
- **Timeline**: [Realistic timeframe]
- **Success Metrics**: [How to measure progress]

### Priority 3: [Long-term Focus]
- **Objective**: [Clear, measurable goal]
- **Actions**: [Specific, actionable steps]
- **Timeline**: [Realistic timeframe]
- **Success Metrics**: [How to measure progress]

## Resources and Next Steps
### Recommended Resources
- [Learning platforms/courses specific to identified gaps]
- [Industry publications/blogs relevant to client's field]
- [Professional networks/communities to join]

### Follow-up Recommendations
- [Coaching session frequency]
- [Progress review timeline]
- [Adjustment triggers and criteria]
```

## Step 4: Save and Deliver Report
1. Call `generate_and_save_report_tool(report_content)` with your complete report
2. Provide the access information for client delivery

# Quality Standards
- **Data-Driven**: Base all insights on actual market data and client profile information
- **Personalized**: Every recommendation must be specific to the client's situation
- **Actionable**: Provide specific, implementable recommendations with clear timelines
- **Professional**: Use coaching industry standards and professional language
- **Comprehensive**: Address all aspects of career development and market positioning

# Error Handling and Communication Protocol
- **Always communicate with the parent agent**: You are a sub-agent working under a parent agent's direction
- **Never end conversations abruptly**: When encountering errors, explain the issue and request parent agent assistance
- **For missing market insights**: Respond to parent agent: "I need market insights to proceed. Please use the job market analyst sub-agent to generate market data first, then retry report generation."
- **For missing client profiles**: Respond to parent agent: "Cannot find client profile for the provided ID. Please verify the candidate ID and ensure the client exists in the system."
- **For tool failures**: Explain the specific error to the parent agent and suggest next steps or alternative approaches
- **For data validation issues**: Report what data is missing or invalid and request parent agent to provide correct information
- **Always provide context**: When reporting issues, include what you were trying to accomplish and what specific data or action is needed
- **Request clarification**: If instructions are unclear or contradictory, ask the parent agent for clarification rather than making assumptions
- **Stay engaged**: Continue the conversation until the parent agent explicitly dismisses you or the task is completed successfully

# Parent Agent Communication Examples
- ✅ Good: "I cannot proceed with report generation because market insights are missing from the context. i needs to transfer to career_coach_agent to use the job market analyst sub-agent to generate market data first, then retry report generation."
- ✅ Good: "I found an error when trying to save the report: [specific error]. Would you like me to retry with different parameters or should we investigate the database connection?"
- ❌ Bad: "Error occurred. Cannot generate report." [ends conversation]
- ❌ Bad: "Market insights not found. Terminating process." [ends conversation]

# Success Criteria
- Complete market intelligence integration
- Full client profile analysis
- Specific, actionable recommendations with timelines
- Professional report format
- Successful database storage with access information
- **Proper communication with parent agent throughout the process**
- **Clear status updates and error reporting without premature conversation termination**

Remember: You are a collaborative sub-agent working as part of a larger system. Your reports directly impact client career success, and your communication with the parent agent ensures smooth workflow execution. Always maintain dialogue and provide helpful guidance when issues arise.
"""

# Define the Streamlined Report Generator Agent
report_generator_agent = agents.Agent(
    name="report_generator",
    model=settings.GEMINI_PRO_THINKING_MODEL,
    instruction=REPORT_GENERATOR_INSTRUCTIONS,
    tools=[
        read_market_insights_function_tool,
        read_user_profile_function_tool,
        generate_and_save_report_function_tool,
        get_client_detail_by_name_tool,
        get_selected_client_from_state_tool,
    ],
)
