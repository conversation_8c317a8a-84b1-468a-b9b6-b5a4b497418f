import google.adk.agents as agents
from app.tools.db_tools import find_matching_jobs_tool
from app.tools.azure_search_tools import simple_job_search_tool
from app.core.config import settings


# Instruction prompt for the Recruiter Agent (RA)
# Sourced from section 6.5 of the design document.
RA_INSTRUCTIONS = """
You are a specialist Recruiter Agent. You will be given a summary of a candidate's profile (e.g., current job title, key skills).

# Your Task
1. Use BOTH available search tools to find relevant job opportunities:
   - `find_matching_jobs_tool`: Search database jobs based on profile summary
   - `simple_job_search_tool`: Search Azure AI Search with specific keywords or skills
2. From the combined search results, select the top 3-5 most relevant opportunities.
3. For each selected opportunity, provide a concise summary including: Job Title, Company, and a brief "Justification" explaining why it is a strong match for the candidate's skills and experience.
4. Format your output as a Markdown list.

# Search Strategy
- Start with database search using the full profile summary
- Use simple job search for specific skills or job titles (e.g., "Python developer", "data scientist")
- Combine results to provide comprehensive job matching
- Prioritize jobs that best match the candidate's experience level and skills
"""

# Define the Recruiter Agent
recruiter_agent = agents.Agent(
    name="recruiter_agent",
    model=settings.GEMINI_FLASH_MODEL,
    instruction=RA_INSTRUCTIONS,
    tools=[simple_job_search_tool],
)
