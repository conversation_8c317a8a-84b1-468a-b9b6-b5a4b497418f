from google.adk.tools.tool_context import ToolContext
from google.adk.tools.function_tool import FunctionTool

async def save_report_to_state(tool_context:ToolContext,report:str,report_type:str):
    """
    Saves the market insights report to state.
    
    Args:
        report: The report string to save to state
        report_type: The type of report to save to state
    Returns:
        JSON string containing the status and message
    """
    
    tool_context.state["market_insights"] = report
    return {
        "status": "success",
        "message": "Market insights saved to state"
    }
    
save_report_to_state_tool = FunctionTool(
    func=save_report_to_state,
)

async def get_selected_client_from_state(tool_context:ToolContext):
    """
    Gets the selected client from state.
    """
    client_id = tool_context.state["selected_client_id"]
    if client_id:
        return {
            "message": "Selected client found in state",
            "client_id": client_id
        }
    return {
        "message":"No client selected in state, use another tool to find the client info"
    }

get_selected_client_from_state_tool = FunctionTool(
    func=get_selected_client_from_state,
)