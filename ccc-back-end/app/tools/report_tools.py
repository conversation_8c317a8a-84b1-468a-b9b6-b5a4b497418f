from google.adk.tools.function_tool import FunctionTool

from app.db.session import SessionL<PERSON>al
from app.db import crud, schemas
from sqlalchemy.orm import Session
import json
from google.adk.tools.tool_context import ToolContext


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_and_save_report(
    report_content: str, client_id: str, tool_context:ToolContext, db: Session
):
    """Uses the report generator service and saves the report to the database."""
    coach_id = tool_context.state.get("coach_id")
    report_data = schemas.ReportCreate(
        client_id=client_id, report_content=report_content
    )
    report = crud.create_report(db=db, report=report_data, coach_id=coach_id)
    tool_context.save_artifact("report_generated", {
        "report_id": report.id,
    })
    return json.dumps(report.to_dict())


create_and_save_report_tool = FunctionTool(
    func=create_and_save_report,
)
