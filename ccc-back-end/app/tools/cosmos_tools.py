from google.adk.tools.function_tool import FunctionTool
from app.services.cosmos_client import cosmos_client, profile_cosmos_service, job_cosmos_service
import json
import asyncio
import concurrent.futures
import time
from google.adk.tools.tool_context import ToolContext

def get_client_profile_from_cosmos(tool_context:ToolContext, cosmos_user_id: str):
    """
    Fetches a user profile from Cosmos DB using the provided user ID.

    Args:
        cosmos_user_id: The unique identifier for the user in Cosmos DB.

    Returns:
        JSON string containing the user profile data or error message if profile not found.
    """
    profile = cosmos_client.get_client_profile(cosmos_user_id)
    if profile:
        profile_str = json.dumps(profile)
        tool_context.state["selected_client"] = profile_str
        return json.dumps(profile)
    return json.dumps({"error": "Profile not found."})


async def search_profiles_from_cosmos(
    job_search_status: str = None,
    preferred_industries: list = None,
    limit: int = 10
):
    """
    Search for profiles in the talent pool based on job search status and industry preferences.

    Args:
        job_search_status: Filter profiles by their job search status (e.g., "active", "passive").
        preferred_industries: List of industry names to filter profiles by.
        limit: Maximum number of profiles to return (default: 10).

    Returns:
        JSON string containing matching profiles or error message if search fails.
    """
    try:
        profiles = await profile_cosmos_service.get_profiles(
            limit=limit,
            job_search_status=job_search_status,
            preferred_industries=preferred_industries
        )
        return json.dumps(profiles)
    except Exception as e:
        return json.dumps({"error": f"Failed to search profiles: {str(e)}"})


async def search_jobs_from_cosmos(
    query: str = None,
    status: str = "active",
    company_id: str = None,
    limit: int = 10
):
    """
    Search for jobs in the job database based on various criteria.

    Args:
        query: Search query string to match against job titles, descriptions, or requirements.
        status: Job status filter (default: "active").
        company_id: Filter jobs by specific company ID.
        limit: Maximum number of jobs to return (default: 10).

    Returns:
        JSON string containing matching jobs or error message if search fails.
    """
    try:
        jobs = await job_cosmos_service.search_jobs(
            query=query,
            status=status,
            company_id=company_id,
            limit=limit
        )
        return json.dumps(jobs)
    except Exception as e:
        return json.dumps({"error": f"Failed to search jobs: {str(e)}"})


async def get_recent_jobs_for_market_analysis(
    limit: int = 20,
    status: str = "active"
):
    """
    Retrieve the most recent active job postings optimized for market analysis.
    Returns structured data with relevant fields for job market trend analysis.

    Args:
        limit: Maximum number of recent jobs to retrieve (default: 20).
        status: Job status filter (default: "active").

    Returns:
        JSON string containing structured job data for market analysis including:
        - total_jobs: Number of jobs retrieved
        - jobs: Array of job objects with market-relevant fields
        - analysis_timestamp: Timestamp when analysis was performed
        Returns error message if retrieval fails.
    """
    try:
        jobs = await job_cosmos_service.get_jobs(
            limit=limit,
            status=status
        )
        
        # Filter and structure data for market analysis
        market_jobs = []
        for job in jobs:
            market_job = {
                "id": job.get("id"),
                "title": job.get("title"),
                "company": job.get("company", {}).get("name") if job.get("company") else "Unknown",
                "industry": job.get("company", {}).get("industry") if job.get("company") else None,
                "location": job.get("location", {}),
                "jobType": job.get("jobType"),
                "experienceLevel": job.get("experienceLevel"),
                "skills": job.get("requiredSkills", []),
                "technologies": job.get("technologies", []),
                "workArrangement": job.get("workArrangement"),
                "salaryRange": job.get("salaryRange"),
                "postedDate": job.get("createdAt"),
                "description": job.get("description", "")[:500] + "..." if len(job.get("description", "")) > 500 else job.get("description", ""),
                "benefits": job.get("benefits", []),
                "requirements": job.get("requirements", [])
            }
            market_jobs.append(market_job)
        
        return json.dumps({
            "total_jobs": len(market_jobs),
            "jobs": market_jobs,
            "analysis_timestamp": time.time()
        })
    except Exception as e:
        return json.dumps({"error": f"Failed to retrieve recent jobs for market analysis: {str(e)}"})


async def search_profiles_sync(
    tool_context: ToolContext,
    job_search_status: str = None,
    preferred_industries: list = None,
    limit: int = 10
):
    """
    Async wrapper for profile search in the talent pool.

    Args:
        tool_context: The tool context
        job_search_status: Filter profiles by their job search status (e.g., "active", "passive").
        preferred_industries: List of industry names to filter profiles by.
        limit: Maximum number of profiles to return (default: 10).

    Returns:
        JSON string containing matching profiles or error message if search fails.
    """
    return await search_profiles_from_cosmos(
        job_search_status=job_search_status,
        preferred_industries=preferred_industries,
        limit=limit
    )


async def search_jobs_sync(
    tool_context: ToolContext,
    query: str = None,
    status: str = "active",
    company_id: str = None,
    limit: int = 10
):
    """
    Async wrapper for job search in the job database.

    Args:
        tool_context: The tool context
        query: Search query string to match against job titles, descriptions, or requirements.
        status: Job status filter (default: "active").
        company_id: Filter jobs by specific company ID.
        limit: Maximum number of jobs to return (default: 10).

    Returns:
        JSON string containing matching jobs or error message if search fails.
    """
    return await search_jobs_from_cosmos(
        query=query,
        status=status,
        company_id=company_id,
        limit=limit
    )


async def get_recent_jobs_for_market_analysis_sync(
    tool_context: ToolContext,
    limit: int = 20,
    status: str = "active"
):
    """
    Async wrapper for getting recent jobs optimized for market analysis.

    Args:
        tool_context: The tool context
        limit: Maximum number of recent jobs to retrieve (default: 20).
        status: Job status filter (default: "active").

    Returns:
        JSON string containing structured job data for market analysis.
    """
    return await get_recent_jobs_for_market_analysis(limit=limit, status=status)


# Function tools for the agent system
get_client_profile_from_cosmos_tool = FunctionTool(
    func=get_client_profile_from_cosmos,
)

search_profiles_tool = FunctionTool(
    func=search_profiles_sync,
)

search_jobs_tool = FunctionTool(
    func=search_jobs_sync,
)

# Specialized tool for market analysis
get_recent_jobs_for_market_analysis_tool = FunctionTool(
    func=get_recent_jobs_for_market_analysis_sync,
)
