from google.adk.tools.function_tool import FunctionTool
from google.adk.tools.tool_context import ToolContext
from app.db import crud
from sqlalchemy.orm import Session
from app.db.session import SessionLocal, AsyncSessionLocal
from app.services.cosmos_client import profile_cosmos_service
import json
import asyncio
import uuid
import logging

# Set up logger for this module
logger = logging.getLogger(__name__)


async def get_client_by_name_async(client_name: str, coach_id: str):
    """Async version: Queries the database for a client by name for the current coach."""
    async with AsyncSessionLocal() as db:
        try:
            coach_uuid = uuid.UUID(coach_id)
            clients = await crud.get_clients_by_coach_async(db, coach_id=coach_uuid)
            for client in clients:
                if client.client_full_name.lower() == client_name.lower():
                    return json.dumps(client.to_dict())
            return json.dumps({"error": "Client not found."})
        except (ValueError, TypeError) as e:
            return json.dumps({"error": f"Invalid coach ID format: {str(e)}"})


async def get_client_by_name_sync(tool_context: ToolContext, client_name: str):
    """Async wrapper: Queries the database for a client by name for the current coach."""
    coach_id = tool_context.state.get("coach_id")
    return await get_client_by_name_async(client_name, str(coach_id))


def get_client_by_name(client_name: str, coach_id: str):
    """Queries the database for a client by name for the current coach."""
    import asyncio
    
    # Check if we're in an async context
    try:
        loop = asyncio.get_running_loop()
        # If we're in an event loop, we can't use asyncio.run()
        return json.dumps({"error": "Function called from async context - use async wrapper instead"})
    except RuntimeError:
        # No event loop running, safe to use asyncio.run()
        return asyncio.run(get_client_by_name_async(client_name, str(coach_id)))


async def get_coach_clients_summary_async(coach_id: str):
    """Async version: Queries for a summary of the coach's clients."""
    async with AsyncSessionLocal() as db:
        try:
            coach_uuid = uuid.UUID(coach_id)
            clients = await crud.get_clients_by_coach_async(db, coach_id=coach_uuid)
            summary = {
                "total_clients": len(clients),
                "clients": [client.client_full_name for client in clients],
            }
            return json.dumps(summary)
        except (ValueError, TypeError) as e:
            return json.dumps({"error": f"Invalid coach ID format: {str(e)}"})


async def get_coach_clients_summary_sync(tool_context: ToolContext):
    """Async wrapper: Queries for a summary of the coach's clients."""
    coach_id = tool_context.state.get("coach_id")
    return await get_coach_clients_summary_async(str(coach_id))


def get_coach_clients_summary(coach_id: str):
    """Queries for a summary of the coach's clients."""
    import asyncio
    
    # Check if we're in an async context
    try:
        loop = asyncio.get_running_loop()
        # If we're in an event loop, we can't use asyncio.run()
        return json.dumps({"error": "Function called from async context - use async wrapper instead"})
    except RuntimeError:
        # No event loop running, safe to use asyncio.run()
        return asyncio.run(get_coach_clients_summary_async(coach_id))


async def get_coach_clients_with_profiles_async(coach_id: str, limit: int = 20):
    """
    Retrieves the coach's clients with their detailed profile information from Cosmos DB.
    Filters out sensitive information like gender for privacy protection.
    
    Args:
        coach_id: The coach's unique identifier
        limit: Maximum number of clients to retrieve (default: 20)
    
    Returns:
        JSON string containing clients with their profile data, with sensitive info filtered out
    """
    async with AsyncSessionLocal() as db:
        try:
            # Convert coach_id string to UUID
            coach_uuid = uuid.UUID(coach_id)
            
            # Get clients from PostgreSQL
            clients = await crud.get_clients_by_coach_async(db, coach_id=coach_uuid, limit=limit)
            
            # Get detailed profile information for each client from Cosmos DB
            client_profiles = []
            for client in clients:
                try:
                    # Get profile from Cosmos DB using cosmos_profile_id
                    profile = await profile_cosmos_service.get_profile(str(client.cosmos_profile_id))
                    
                    if profile:
                        # Filter out sensitive information
                        filtered_profile = filter_sensitive_profile_data(profile)
                        
                        # Combine client info with filtered profile data
                        client_profile = {
                            "client_id": str(client.id),
                            "client_full_name": client.client_full_name,
                            "added_at": client.added_at.isoformat(),
                            "profile": filtered_profile
                        }
                        client_profiles.append(client_profile)
                    else:
                        # If profile not found in Cosmos, still include basic client info
                        client_profile = {
                            "client_id": str(client.id),
                            "client_full_name": client.client_full_name,
                            "added_at": client.added_at.isoformat(),
                            "profile": None
                        }
                        client_profiles.append(client_profile)
                        
                except Exception as e:
                    # Include client without profile data if there's an error
                    client_profile = {
                        "client_id": str(client.id),
                        "client_full_name": client.client_full_name,
                        "added_at": client.added_at.isoformat(),
                        "profile": None,
                        "error": f"Error retrieving profile: {str(e)}"
                    }
                    client_profiles.append(client_profile)
            
            return json.dumps({
                "total_clients": len(client_profiles),
                "clients": client_profiles
            })
            
        except (ValueError, TypeError) as e:
            return json.dumps({"error": f"Invalid coach ID format: {str(e)}"})
        except Exception as e:
            return json.dumps({"error": f"Failed to retrieve clients with profiles: {str(e)}"})


def filter_sensitive_profile_data(profile: dict) -> dict:
    """
    Filters out sensitive information from a profile for privacy protection.
    Removes or masks sensitive fields like gender, personal identifiers, etc.
    
    Args:
        profile: The original profile dictionary
        
    Returns:
        Filtered profile dictionary with sensitive information removed
    """
    if not profile:
        return profile
    
    # Create a copy to avoid modifying the original
    filtered = profile.copy()
    
    # Remove or filter sensitive fields from personalInfo
    if "personalInfo" in filtered:
        personal_info = filtered["personalInfo"].copy()
        
        # Remove sensitive fields
        sensitive_fields = [
            "gender", "dateOfBirth", "age", "maritalStatus", 
            "nationality", "socialSecurityNumber", "taxId",
            "emergencyContact", "personalId", "passportNumber"
        ]
        
        for field in sensitive_fields:
            personal_info.pop(field, None)
        
        filtered["personalInfo"] = personal_info
    
    # Filter contact information to remove sensitive details
    if "contactInfo" in filtered:
        contact_info = filtered["contactInfo"].copy()
        
        # Keep only professional contact info, mask personal details
        if "emails" in contact_info:
            emails = contact_info["emails"]
            filtered_emails = []
            for email in emails:
                if isinstance(email, dict):
                    # Keep only email address, remove other details
                    filtered_email = {
                        "email": email.get("email", ""),
                        "type": email.get("type", "")
                    }
                    filtered_emails.append(filtered_email)
                else:
                    filtered_emails.append(email)
            contact_info["emails"] = filtered_emails
        
        # Mask phone numbers partially for privacy
        if "phones" in contact_info:
            phones = contact_info["phones"]
            filtered_phones = []
            for phone in phones:
                if isinstance(phone, dict) and "number" in phone:
                    number = phone["number"]
                    # Mask middle digits: +1234567890 -> +123***7890
                    if len(number) > 6:
                        masked_number = number[:3] + "***" + number[-4:]
                    else:
                        masked_number = "***" + number[-2:] if len(number) > 2 else "***"
                    
                    filtered_phone = {
                        "number": masked_number,
                        "type": phone.get("type", "")
                    }
                    filtered_phones.append(filtered_phone)
                else:
                    filtered_phones.append(phone)
            contact_info["phones"] = filtered_phones
        
        filtered["contactInfo"] = contact_info
    
    # Remove any other potentially sensitive top-level fields
    sensitive_top_level = [
        "personalIdentifiers", "financialInfo", "medicalInfo",
        "familyInfo", "privateNotes", "internalNotes"
    ]
    
    for field in sensitive_top_level:
        filtered.pop(field, None)
    
    return filtered


async def get_coach_clients_with_profiles_sync(tool_context:ToolContext, limit: int = 20):
    """
    Synchronous wrapper for getting coach clients with their profiles.
    
    Args:
        tool_context: The tool context
        limit: Maximum number of clients to retrieve (default: 20)
    
    Returns:
        JSON string containing clients with their profile data, with sensitive info filtered out
    """
    coach_id = tool_context.state.get("coach_id")
   
    return await get_coach_clients_with_profiles_async(str(coach_id), limit)


async def find_matching_jobs_async(profile_keywords: list):
    """Async version: Searches for jobs matching a profile using Azure Search."""
    try:
        from app.services.azure_search import azure_search_service
        
        # Use Azure Search to find matching jobs
        search_results = await azure_search_service.find_matching_jobs_by_profile(
            profile_keywords=profile_keywords,
            top=20,
            boost_recent=True
        )
        
        return json.dumps(search_results)
        
    except Exception as e:
        return json.dumps({
            "error": f"Failed to find matching jobs: {str(e)}",
            "profile_keywords": profile_keywords,
            "jobs": [],
            "total_count": 0
        })
    


async def find_matching_jobs_sync(tool_context: ToolContext, profile_keywords: list):
    """Async wrapper: Searches for jobs matching a profile."""
    return await find_matching_jobs_async(profile_keywords)


def find_matching_jobs(profile_keywords: list):
    """Searches for jobs matching a profile."""
    import asyncio
    
    # Check if we're in an async context
    try:
        loop = asyncio.get_running_loop()
        # If we're in an event loop, we can't use asyncio.run()
        return json.dumps({
            "error": "Function called from async context - use async wrapper instead",
            "profile_keywords": profile_keywords,
            "jobs": [],
            "total_count": 0
        })
    except RuntimeError:
        # No event loop running, safe to use asyncio.run()
        return asyncio.run(find_matching_jobs_async(profile_keywords))


get_client_by_name_tool = FunctionTool(
    func=get_client_by_name_sync,
)


get_coach_clients_summary_tool = FunctionTool(
    func=get_coach_clients_summary_sync,
)


get_coach_clients_with_profiles_tool = FunctionTool(
    func=get_coach_clients_with_profiles_sync,
)


find_matching_jobs_tool = FunctionTool(
    func=find_matching_jobs_sync,
)


async def get_client_detail_by_name_async(search_name: str, coach_id: str):
    """
    Async version: Searches for clients by partial or full name and returns complete client details with profiles.
    
    Args:
        search_name: Partial or full name to search for (case-insensitive)
        coach_id: The coach's unique identifier (defaults to current coach)
    
    Returns:
        JSON string containing matching clients with their complete profile data
    """
    logger.info(f"Starting client search - search_name: '{search_name}', coach_id: '{coach_id}'")
    
    async with AsyncSessionLocal() as db:
        try:
            # Convert coach_id to UUID
            logger.debug(f"Converting coach_id '{coach_id}' to UUID")
            coach_uuid = uuid.UUID(coach_id)
            logger.debug(f"Successfully converted coach_id to UUID: {coach_uuid}")
            
            # Search for clients by name using the new CRUD function
            logger.debug(f"Searching for clients with name pattern: '{search_name}'")
            clients = await crud.get_clients_by_name_async(db, coach_uuid, search_name)
            logger.info(f"Database search returned {len(clients) if clients else 0} matching clients")
            
            if not clients:
                logger.warning(f"No clients found matching name: '{search_name}' for coach: {coach_id}")
                return json.dumps({
                    "status": "not_found",
                    "message": f"No clients found matching name: {search_name}",
                    "search_term": search_name,
                    "total_matches": 0,
                    "clients": []
                })
            
            # Get detailed profile information for each matching client from Cosmos DB
            client_details = []
            logger.debug(f"Processing {len(clients)} clients to fetch profiles from Cosmos DB")
            
            for i, client in enumerate(clients, 1):
                logger.debug(f"Processing client {i}/{len(clients)}: {client.client_full_name} (ID: {client.id})")
                try:
                    # Get profile from Cosmos DB using cosmos_profile_id
                    cosmos_profile_id = str(client.cosmos_profile_id)
                    logger.debug(f"Fetching profile from Cosmos DB for profile_id: {cosmos_profile_id}")
                    
                    profile = await profile_cosmos_service.get_profile(cosmos_profile_id)
                    
                    if profile:
                        logger.debug(f"Successfully retrieved profile from Cosmos DB for client: {client.client_full_name}")
                        # Filter out sensitive information
                        filtered_profile = filter_sensitive_profile_data(profile)
                        
                        # Create complete client detail object
                        client_detail = {
                            "client_id": str(client.id),
                            "client_full_name": client.client_full_name,
                            "cosmos_user_id": str(client.cosmos_user_id),
                            "cosmos_profile_id": str(client.cosmos_profile_id),
                            "added_at": client.added_at.isoformat(),
                            "profile": filtered_profile
                        }
                        client_details.append(client_detail)
                        logger.debug(f"Added client detail for: {client.client_full_name}")
                    else:
                        logger.warning(f"Profile not found in Cosmos DB for client: {client.client_full_name}, profile_id: {cosmos_profile_id}")
                        # If profile not found in Cosmos, still include basic client info
                        client_detail = {
                            "client_id": str(client.id),
                            "client_full_name": client.client_full_name,
                            "cosmos_user_id": str(client.cosmos_user_id),
                            "cosmos_profile_id": str(client.cosmos_profile_id),
                            "added_at": client.added_at.isoformat(),
                            "profile": None,
                            "warning": "Profile not found in Cosmos DB"
                        }
                        client_details.append(client_detail)
                        logger.debug(f"Added client detail without profile for: {client.client_full_name}")
                        
                except Exception as e:
                    logger.error(f"Error retrieving profile for client {client.client_full_name} (ID: {client.id}, profile_id: {client.cosmos_profile_id}): {str(e)}", exc_info=True)
                    # Include client without profile data if there's an error
                    client_detail = {
                        "client_id": str(client.id),
                        "client_full_name": client.client_full_name,
                        "cosmos_user_id": str(client.cosmos_user_id),
                        "cosmos_profile_id": str(client.cosmos_profile_id),
                        "added_at": client.added_at.isoformat(),
                        "profile": None,
                        "error": f"Error retrieving profile: {str(e)}"
                    }
                    client_details.append(client_detail)
                    logger.debug(f"Added client detail with error for: {client.client_full_name}")
            
            result = {
                "status": "success",
                "search_term": search_name,
                "total_matches": len(client_details),
                "clients": client_details
            }
            logger.info(f"Successfully completed client search for '{search_name}' - found {len(client_details)} clients")
            return json.dumps(result)
            
        except (ValueError, TypeError) as e:
            error_msg = f"Invalid coach ID format: {str(e)}"
            logger.error(f"Coach ID validation failed for '{coach_id}': {error_msg}", exc_info=True)
            return json.dumps({
                "status": "error",
                "message": error_msg,
                "search_term": search_name,
                "total_matches": 0,
                "clients": []
            })
        except Exception as e:
            error_msg = f"Failed to search clients by name: {str(e)}"
            logger.error(f"Unexpected error in get_client_detail_by_name_async for search_name='{search_name}', coach_id='{coach_id}': {error_msg}", exc_info=True)
            return json.dumps({
                "status": "error",
                "message": error_msg,
                "search_term": search_name,
                "total_matches": 0,
                "clients": []
            })


async def get_client_detail_by_name(tool_context: ToolContext, search_name: str):
    """
    Async wrapper for searching clients by partial or full name and returning complete client details.
    This function is designed to work with the existing event loop in the agent framework.
    
    Args:
        tool_context: The tool context
        search_name: Partial or full name to search for (case-insensitive)
    
    Returns:
        JSON string containing matching clients with their complete profile data
    """
    coach_id = tool_context.state.get("coach_id")
    return await get_client_detail_by_name_async(search_name, str(coach_id))




get_client_detail_by_name_tool = FunctionTool(
    func=get_client_detail_by_name,
)
