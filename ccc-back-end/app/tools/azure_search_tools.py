import json
import async<PERSON>
from typing import List, Dict, Any, Optional
from google.adk.tools.function_tool import FunctionTool
from google.adk.tools.tool_context import ToolContext
from app.services.azure_search import azure_search_service
import logging

# Configure logger
logger = logging.getLogger(__name__)


async def search_jobs_with_azure_search_async(
    query: str,
    top: int = 20,
    skip: int = 0,
    filters: Optional[str] = None,
    semantic_ranking: bool = True
) -> str:
    """
    Search for jobs using Azure Cognitive Search with a text query.
    
    Args:
        query: Search query string (keywords, job titles, skills, company names, etc.)
        top: Number of results to return (default: 20)
        skip: Number of results to skip for pagination (default: 0)
        filters: OData filter expression for additional filtering (e.g., "status eq 'active'")
        semantic_ranking: Whether to use semantic ranking for better relevance (default: True)
    
    Returns:
        JSON string containing search results with job matches and metadata
    
    Examples:
        - search_jobs_with_azure_search_async("Python developer")
        - search_jobs_with_azure_search_async("machine learning engineer", top=10)
        - search_jobs_with_azure_search_async("software engineer", filters="location eq 'San Francisco'")
    """
    try:
        search_results = await azure_search_service.search_jobs_by_query(
            query=query,
            top=top,
            skip=skip,
            filters=filters,
            semantic_ranking=semantic_ranking
        )
        
        return json.dumps(search_results)
        
    except Exception as e:
        logger.error(f"Error in Azure Search job query: {e}")
        return json.dumps({
            "error": f"Azure Search failed: {str(e)}",
            "query": query,
            "jobs": [],
            "total_count": 0,
            "returned_count": 0,
            "skip": skip,
            "top": top
        })


async def search_jobs_with_azure_search_sync(
    tool_context: ToolContext,
    query: str,
    top: int = 20,
    skip: int = 0,
    filters: Optional[str] = None,
    semantic_ranking: bool = True
) -> str:
    """
    Async wrapper for Azure Search job search.
    
    Args:
        tool_context: The tool context
        query: Search query string (keywords, job titles, skills, company names, etc.)
        top: Number of results to return (default: 20)
        skip: Number of results to skip for pagination (default: 0)
        filters: OData filter expression for additional filtering
        semantic_ranking: Whether to use semantic ranking for better relevance
    
    Returns:
        JSON string containing search results with job matches and metadata
    """
    return await search_jobs_with_azure_search_async(
        query=query,
        top=top,
        skip=skip,
        filters=filters,
        semantic_ranking=semantic_ranking
    )


def search_jobs_with_azure_search(
    query: str,
    top: int = 20,
    skip: int = 0,
    filters: Optional[str] = None,
    semantic_ranking: bool = True
) -> str:
    """
    Synchronous wrapper for Azure Search job search.
    
    Args:
        query: Search query string (keywords, job titles, skills, company names, etc.)
        top: Number of results to return (default: 20)
        skip: Number of results to skip for pagination (default: 0)
        filters: OData filter expression for additional filtering
        semantic_ranking: Whether to use semantic ranking for better relevance
    
    Returns:
        JSON string containing search results with job matches and metadata
    """
    import asyncio
    
    # Check if we're in an async context
    try:
        loop = asyncio.get_running_loop()
        # If we're in an event loop, we can't use asyncio.run()
        return json.dumps({
            "error": "Function called from async context - use async wrapper instead",
            "query": query,
            "jobs": [],
            "total_count": 0
        })
    except RuntimeError:
        # No event loop running, safe to use asyncio.run()
        return asyncio.run(search_jobs_with_azure_search_async(
            query=query,
            top=top,
            skip=skip,
            filters=filters,
            semantic_ranking=semantic_ranking
        ))


async def find_matching_jobs_by_profile_async(
    profile_keywords: List[str],
    job_preferences: Optional[Dict[str, Any]] = None,
    top: int = 20,
    skip: int = 0,
    boost_recent: bool = True
) -> str:
    """
    Find matching jobs for a candidate profile using Azure Search.
    
    Args:
        profile_keywords: List of keywords/skills from the candidate's profile
        job_preferences: Optional dictionary with job preferences:
            - preferred_location: String (e.g., "San Francisco", "Remote")
            - min_salary: Integer (minimum salary requirement)
            - max_salary: Integer (maximum salary requirement)
            - job_type: String (e.g., "full-time", "part-time", "contract")
            - remote_work: Boolean (True if remote work is preferred)
            - preferred_industries: List of strings (e.g., ["Technology", "Healthcare"])
        top: Number of results to return (default: 20)
        skip: Number of results to skip for pagination (default: 0)
        boost_recent: Whether to prioritize recent job postings (default: True)
    
    Returns:
        JSON string containing matching jobs with relevance scores and metadata
    
    Examples:
        - find_matching_jobs_by_profile_async(["Python", "Machine Learning", "TensorFlow"])
        - find_matching_jobs_by_profile_async(["Java", "Spring Boot"], {"preferred_location": "New York", "remote_work": True})
    """
    try:
        search_results = await azure_search_service.find_matching_jobs_by_profile(
            profile_keywords=profile_keywords,
            job_preferences=job_preferences,
            top=top,
            skip=skip,
            boost_recent=boost_recent
        )
        
        return json.dumps(search_results)
        
    except Exception as e:
        logger.error(f"Error in profile-based job matching: {e}")
        return json.dumps({
            "error": f"Profile-based job matching failed: {str(e)}",
            "profile_keywords": profile_keywords,
            "job_preferences": job_preferences,
            "jobs": [],
            "total_count": 0,
            "returned_count": 0,
            "skip": skip,
            "top": top,
            "match_type": "profile_based"
        })


async def find_matching_jobs_by_profile_sync(
    tool_context: ToolContext,
    query:str,
    top: int = 20,
    skip: int = 0,
    boost_recent: bool = True
) -> str:
    """
    Async wrapper for profile-based job matching.
    
    Args:
        tool_context: The tool context
        query: Search query string (keywords, job titles, skills, company names, etc.)
        job_preferences: Optional job preferences dictionary
        top: Number of results to return (default: 20)
        skip: Number of results to skip for pagination (default: 0)
        boost_recent: Whether to prioritize recent job postings (default: True)
    
    Returns:
        JSON string containing matching jobs with relevance scores and metadata
    """
    return await find_matching_jobs_by_profile_async(
        profile_keywords=profile_keywords,
        job_preferences=job_preferences,
        top=top,
        skip=skip,
        boost_recent=boost_recent
    )


def find_matching_jobs_by_profile(
    profile_keywords: List[str],
    job_preferences: Optional[Dict[str, Any]] = None,
    top: int = 20,
    skip: int = 0,
    boost_recent: bool = True
) -> str:
    """
    Synchronous wrapper for profile-based job matching.
    
    Args:
        profile_keywords: List of keywords/skills from the candidate's profile
        job_preferences: Optional job preferences dictionary
        top: Number of results to return (default: 20)
        skip: Number of results to skip for pagination (default: 0)
        boost_recent: Whether to prioritize recent job postings (default: True)
    
    Returns:
        JSON string containing matching jobs with relevance scores and metadata
    """
    import asyncio
    
    # Check if we're in an async context
    try:
        loop = asyncio.get_running_loop()
        # If we're in an event loop, we can't use asyncio.run()
        return json.dumps({
            "error": "Function called from async context - use async wrapper instead",
            "profile_keywords": profile_keywords,
            "jobs": [],
            "total_count": 0
        })
    except RuntimeError:
        # No event loop running, safe to use asyncio.run()
        return asyncio.run(find_matching_jobs_by_profile_async(
            profile_keywords=profile_keywords,
            job_preferences=job_preferences,
            top=top,
            skip=skip,
            boost_recent=boost_recent
        ))


async def get_job_suggestions_async(
    partial_query: str,
    top: int = 10
) -> str:
    """
    Get job suggestions for autocomplete functionality using Azure Search.
    
    Args:
        partial_query: Partial search query for suggestions (e.g., "soft", "data sci")
        top: Number of suggestions to return (default: 10)
    
    Returns:
        JSON string containing job title suggestions with company and location info
    
    Examples:
        - get_job_suggestions_async("software")
        - get_job_suggestions_async("data", top=5)
    """
    try:
        suggestions = await azure_search_service.suggest_jobs(
            partial_query=partial_query,
            top=top
        )
        
        return json.dumps(suggestions)
        
    except Exception as e:
        logger.error(f"Error getting job suggestions: {e}")
        return json.dumps({
            "error": f"Job suggestions failed: {str(e)}",
            "query": partial_query,
            "suggestions": [],
            "count": 0
        })


async def get_job_suggestions_sync(
    tool_context: ToolContext,
    partial_query: str,
    top: int = 10
) -> str:
    """
    Async wrapper for job suggestions.
    
    Args:
        tool_context: The tool context
        partial_query: Partial search query for suggestions
        top: Number of suggestions to return (default: 10)
    
    Returns:
        JSON string containing job title suggestions
    """
    return await get_job_suggestions_async(partial_query, top)


def get_job_suggestions(
    partial_query: str,
    top: int = 10
) -> str:
    """
    Synchronous wrapper for job suggestions.
    
    Args:
        partial_query: Partial search query for suggestions
        top: Number of suggestions to return
    
    Returns:
        JSON string containing job suggestions
    """
    import asyncio
    
    # Check if we're in an async context
    try:
        loop = asyncio.get_running_loop()
        # If we're in an event loop, we can't use asyncio.run()
        return json.dumps({
            "error": "Function called from async context - use async wrapper instead",
            "suggestions": [],
            "query": partial_query,
            "count": 0
        })
    except RuntimeError:
        # No event loop running, safe to use asyncio.run()
        return asyncio.run(get_job_suggestions_async(partial_query=partial_query, top=top))


async def simple_job_search_async(query: str, limit: int = 10) -> str:
    """
    Simple job search function for agents to find jobs using a query word.
    
    This is a simplified version designed for agent use - just provide a query word
    and get back relevant job results. Perfect for agents that need to quickly
    search for jobs without complex parameters.
    
    Args:
        query: Search query word or phrase (e.g., "Python", "data scientist", "remote developer")
        limit: Maximum number of jobs to return (default: 10, max: 50)
    
    Returns:
        JSON string with simplified job results containing:
        - jobs: List of matching jobs with essential fields
        - total_found: Total number of matches
        - query_used: The search query that was used
        - success: Boolean indicating if search was successful
    
    Examples:
        - simple_job_search_async("Python")
        - simple_job_search_async("data scientist") 
        - simple_job_search_async("remote developer", limit=5)
    """
    try:
        # Limit the results to prevent overwhelming responses
        search_limit = min(limit, 50)
        
        # Use the existing Azure Search service with simplified parameters
        search_results = await azure_search_service.search_jobs_by_query(
            query=query,
            top=search_limit,
            skip=0,
            semantic_ranking=True,  # Better relevance
            select=["id", "title", "company_name", "location", "job_type", "work_arrangement", "salary_min", "salary_max", "created_at", "description"]
        )
        
        # Simplify the response for agent consumption
        simplified_jobs = []
        for job in search_results.get("jobs", []):
            simplified_job = {
                "id": job.get("id", ""),
                "title": job.get("title", ""),
                "company": job.get("company_name", ""),
                "location": job.get("location", ""),
                "type": job.get("job_type", ""),
                "work_arrangement": job.get("work_arrangement", ""),
                "salary_range": f"{job.get('salary_min', 'N/A')} - {job.get('salary_max', 'N/A')}",
                "posted_date": job.get("created_at", ""),
                "description_preview": job.get("description", "")[:200] + "..." if job.get("description", "") else "",
                "relevance_score": job.get("search_score", 0)
            }
            simplified_jobs.append(simplified_job)
        
        return json.dumps({
            "success": True,
            "jobs": simplified_jobs,
            "total_found": search_results.get("total_count", 0),
            "returned_count": len(simplified_jobs),
            "query_used": query,
            "search_type": "simple"
        })
        
    except Exception as e:
        logger.error(f"Error in simple job search for query '{query}': {e}")
        return json.dumps({
            "success": False,
            "jobs": [],
            "total_found": 0,
            "returned_count": 0,
            "query_used": query,
            "error": f"Search failed: {str(e)}",
            "search_type": "simple"
        })


async def simple_job_search_sync(
    tool_context: ToolContext,
    query: str,
    limit: int = 10
) -> str:
    """
    A simple job search tool for agents to find jobs using a query word. the query word must be azure search query word.
    
    Args:
        tool_context: The tool context
        query: Search query word or phrase
        limit: Maximum number of jobs to return
    
    Returns:
        JSON string with simplified job search results
    """
    return await simple_job_search_async(query=query, limit=limit)


def simple_job_search(query: str, limit: int = 10) -> str:
    """
    Synchronous simple job search function for agents.
    
    Args:
        query: Search query word or phrase (e.g., "Python", "data scientist", "remote")
        limit: Maximum number of jobs to return (default: 10, max: 50)
    
    Returns:
        JSON string with simplified job search results
    """
    import asyncio
    
    # Check if we're in an async context
    try:
        loop = asyncio.get_running_loop()
        # If we're in an event loop, we can't use asyncio.run()
        return json.dumps({
            "success": False,
            "error": "Function called from async context - use async wrapper instead",
            "jobs": [],
            "total_found": 0,
            "query_used": query
        })
    except RuntimeError:
        # No event loop running, safe to use asyncio.run()
        return asyncio.run(simple_job_search_async(query=query, limit=limit))


# Create the simple job search tool for agents
simple_job_search_tool = FunctionTool(
    func=simple_job_search_sync,
)

# Function tools for the agent system
azure_search_jobs_tool = FunctionTool(
    func=search_jobs_with_azure_search_sync,
)

azure_find_matching_jobs_tool = FunctionTool(
    func=find_matching_jobs_by_profile_sync,
)

azure_job_suggestions_tool = FunctionTool(
    func=get_job_suggestions_sync,
) 