import logging
import os
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from google.adk.sessions import DatabaseSessionService
from app.api import auth, dashboard, reports, chat
from app.core.config import settings
from dotenv import load_dotenv

load_dotenv()

# Configure logging for debugging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("app.log")
    ]
)

# Get logger for this module
logger = logging.getLogger(__name__)

# Initialize the ADK DatabaseSessionService
session_service = DatabaseSessionService(db_url=settings.POSTGRES_DSN)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: create ADK database tables
    logger.info("Starting up Career Coach Central API")
    logger.debug("ADK DatabaseSessionService initialized.")
    print("ADK DatabaseSessionService initialized.")
    yield
    # Shutdown: cleanup if needed
    logger.info("Shutting down Career Coach Central API")
    # Add any cleanup code here if necessary


# Initialize the FastAPI application
app = FastAPI(
    title="Career Coach Central API",
    description="API for the Career Coach Central platform.",
    version="1.0.0",
    lifespan=lifespan,
)

logger.debug("FastAPI application initialized")

# Configure CORS middleware
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    logger.debug(f"CORS middleware configured with origins: {settings.BACKEND_CORS_ORIGINS}")

# Mount the API routers
app.include_router(auth.router, prefix="/auth", tags=["Authentication"])
app.include_router(dashboard.router, prefix="/dashboard", tags=["Dashboard"])
app.include_router(reports.router, prefix="/reports", tags=["Reports"])
app.include_router(chat.router, prefix="/chat", tags=["Chat"])

logger.debug("API routers mounted successfully")

# Root endpoint
@app.get("/")
def read_root():
    logger.debug("Root endpoint accessed")
    return {"message": "Welcome to Career Coach Central API"}


# Main entry point for Google Cloud Run
if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", 8080))
    uvicorn.run("app.main:app", host="0.0.0.0", port=port)
