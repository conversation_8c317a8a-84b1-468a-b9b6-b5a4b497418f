import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from app.services.azure_search import AzureSearchService
from app.tools.azure_search_tools import (
    search_jobs_with_azure_search_async,
    find_matching_jobs_by_profile_async,
    get_job_suggestions_async
)


@pytest.fixture
def mock_azure_search_service():
    """Mock Azure Search service for testing."""
    service = Mock(spec=AzureSearchService)
    
    # Mock search_jobs_by_query method
    service.search_jobs_by_query = AsyncMock(return_value={
        "jobs": [
            {
                "id": "job1",
                "title": "Senior Python Developer",
                "company_name": "Tech Corp",
                "location": "San Francisco",
                "skills": ["Python", "Django", "AWS"],
                "search_score": 0.95
            },
            {
                "id": "job2", 
                "title": "Machine Learning Engineer",
                "company_name": "AI Startup",
                "location": "Remote",
                "skills": ["Python", "TensorFlow", "Machine Learning"],
                "search_score": 0.88
            }
        ],
        "total_count": 2,
        "query": "Python developer",
        "returned_count": 2,
        "skip": 0,
        "top": 20
    })
    
    # Mock find_matching_jobs_by_profile method
    service.find_matching_jobs_by_profile = AsyncMock(return_value={
        "jobs": [
            {
                "id": "job3",
                "title": "Full Stack Developer", 
                "company_name": "StartupXYZ",
                "location": "New York",
                "skills": ["JavaScript", "React", "Node.js"],
                "search_score": 0.92
            }
        ],
        "total_count": 1,
        "profile_keywords": ["JavaScript", "React"],
        "job_preferences": {"preferred_location": "New York"},
        "returned_count": 1,
        "skip": 0,
        "top": 20,
        "match_type": "profile_based"
    })
    
    # Mock suggest_jobs method
    service.suggest_jobs = AsyncMock(return_value={
        "suggestions": [
            {
                "text": "Software Engineer",
                "company": "Google",
                "location": "Mountain View",
                "job_id": "job4"
            },
            {
                "text": "Software Developer",
                "company": "Microsoft", 
                "location": "Seattle",
                "job_id": "job5"
            }
        ],
        "query": "soft",
        "count": 2
    })
    
    return service


@pytest.mark.asyncio
async def test_search_jobs_with_azure_search():
    """Test basic job search functionality."""
    with patch('app.tools.azure_search_tools.azure_search_service') as mock_service:
        mock_service.search_jobs_by_query = AsyncMock(return_value={
            "jobs": [{"id": "test_job", "title": "Test Developer"}],
            "total_count": 1,
            "query": "test query",
            "returned_count": 1,
            "skip": 0,
            "top": 20
        })
        
        result = await search_jobs_with_azure_search_async("test query")
        result_data = json.loads(result)
        
        assert result_data["total_count"] == 1
        assert result_data["query"] == "test query"
        assert len(result_data["jobs"]) == 1
        assert result_data["jobs"][0]["title"] == "Test Developer"
        
        # Verify the service was called with correct parameters
        mock_service.search_jobs_by_query.assert_called_once_with(
            query="test query",
            top=20,
            skip=0,
            filters=None,
            semantic_ranking=True
        )


@pytest.mark.asyncio
async def test_find_matching_jobs_by_profile():
    """Test profile-based job matching functionality."""
    with patch('app.tools.azure_search_tools.azure_search_service') as mock_service:
        mock_service.find_matching_jobs_by_profile = AsyncMock(return_value={
            "jobs": [{"id": "matched_job", "title": "Python Developer"}],
            "total_count": 1,
            "profile_keywords": ["Python", "Django"],
            "job_preferences": {"remote_work": True},
            "returned_count": 1,
            "skip": 0,
            "top": 20,
            "match_type": "profile_based"
        })
        
        profile_keywords = ["Python", "Django"]
        job_preferences = {"remote_work": True}
        
        result = await find_matching_jobs_by_profile_async(
            profile_keywords=profile_keywords,
            job_preferences=job_preferences
        )
        result_data = json.loads(result)
        
        assert result_data["total_count"] == 1
        assert result_data["profile_keywords"] == profile_keywords
        assert result_data["job_preferences"] == job_preferences
        assert result_data["match_type"] == "profile_based"
        assert len(result_data["jobs"]) == 1
        
        # Verify the service was called with correct parameters
        mock_service.find_matching_jobs_by_profile.assert_called_once_with(
            profile_keywords=profile_keywords,
            job_preferences=job_preferences,
            top=20,
            skip=0,
            boost_recent=True
        )


@pytest.mark.asyncio
async def test_get_job_suggestions():
    """Test job suggestions functionality."""
    with patch('app.tools.azure_search_tools.azure_search_service') as mock_service:
        mock_service.suggest_jobs = AsyncMock(return_value={
            "suggestions": [
                {"text": "Software Engineer", "company": "TechCorp", "job_id": "job1"},
                {"text": "Software Developer", "company": "DevCorp", "job_id": "job2"}
            ],
            "query": "soft",
            "count": 2
        })
        
        result = await get_job_suggestions_async("soft")
        result_data = json.loads(result)
        
        assert result_data["count"] == 2
        assert result_data["query"] == "soft"
        assert len(result_data["suggestions"]) == 2
        assert result_data["suggestions"][0]["text"] == "Software Engineer"
        
        # Verify the service was called with correct parameters
        mock_service.suggest_jobs.assert_called_once_with(
            partial_query="soft",
            top=10
        )


@pytest.mark.asyncio
async def test_search_jobs_error_handling():
    """Test error handling in job search."""
    with patch('app.tools.azure_search_tools.azure_search_service') as mock_service:
        mock_service.search_jobs_by_query = AsyncMock(side_effect=Exception("Azure Search error"))
        
        result = await search_jobs_with_azure_search_async("test query")
        result_data = json.loads(result)
        
        assert "error" in result_data
        assert "Azure Search failed" in result_data["error"]
        assert result_data["jobs"] == []
        assert result_data["total_count"] == 0


@pytest.mark.asyncio
async def test_find_matching_jobs_error_handling():
    """Test error handling in profile-based job matching."""
    with patch('app.tools.azure_search_tools.azure_search_service') as mock_service:
        mock_service.find_matching_jobs_by_profile = AsyncMock(side_effect=Exception("Profile matching error"))
        
        profile_keywords = ["Python", "Django"]
        result = await find_matching_jobs_by_profile_async(profile_keywords=profile_keywords)
        result_data = json.loads(result)
        
        assert "error" in result_data
        assert "Profile-based job matching failed" in result_data["error"]
        assert result_data["jobs"] == []
        assert result_data["total_count"] == 0
        assert result_data["profile_keywords"] == profile_keywords


@pytest.mark.asyncio 
async def test_job_suggestions_error_handling():
    """Test error handling in job suggestions."""
    with patch('app.tools.azure_search_tools.azure_search_service') as mock_service:
        mock_service.suggest_jobs = AsyncMock(side_effect=Exception("Suggestions error"))
        
        result = await get_job_suggestions_async("soft")
        result_data = json.loads(result)
        
        assert "error" in result_data
        assert "Job suggestions failed" in result_data["error"]
        assert result_data["suggestions"] == []
        assert result_data["count"] == 0 