#!/usr/bin/env python3
"""
Simple test script for the Azure AI Search simple job search functionality.
This script tests the basic functionality of the simple_job_search_tool.
"""

import asyncio
import json
import sys
import os

# Add the app directory to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.tools.azure_search_tools import simple_job_search_async, simple_job_search


async def test_simple_job_search_async():
    """Test the async version of simple job search."""
    print("Testing simple_job_search_async...")
    
    test_queries = [
        "Python",
        "data scientist", 
        "remote developer",
        "software engineer"
    ]
    
    for query in test_queries:
        print(f"\n--- Testing query: '{query}' ---")
        try:
            result = await simple_job_search_async(query, limit=5)
            result_data = json.loads(result)
            
            print(f"Success: {result_data.get('success', False)}")
            print(f"Total found: {result_data.get('total_found', 0)}")
            print(f"Returned: {result_data.get('returned_count', 0)}")
            
            if result_data.get('success') and result_data.get('jobs'):
                print("Sample jobs:")
                for i, job in enumerate(result_data['jobs'][:2]):  # Show first 2 jobs
                    print(f"  {i+1}. {job.get('title', 'N/A')} at {job.get('company', 'N/A')}")
                    print(f"     Location: {job.get('location', 'N/A')}")
                    print(f"     Type: {job.get('type', 'N/A')}")
            
            if result_data.get('error'):
                print(f"Error: {result_data['error']}")
                
        except Exception as e:
            print(f"Exception during test: {e}")


def test_simple_job_search_sync():
    """Test the sync version of simple job search."""
    print("\n\nTesting simple_job_search (sync)...")
    
    try:
        result = simple_job_search("Python developer", limit=3)
        result_data = json.loads(result)
        
        print(f"Success: {result_data.get('success', False)}")
        print(f"Total found: {result_data.get('total_found', 0)}")
        print(f"Query used: {result_data.get('query_used', 'N/A')}")
        
        if result_data.get('error'):
            print(f"Error: {result_data['error']}")
            
    except Exception as e:
        print(f"Exception during sync test: {e}")


async def main():
    """Main test function."""
    print("=== Azure AI Search Simple Job Search Test ===")
    
    # Test async version
    await test_simple_job_search_async()
    
    # Test sync version
    test_simple_job_search_sync()
    
    print("\n=== Test Complete ===")


if __name__ == "__main__":
    asyncio.run(main()) 