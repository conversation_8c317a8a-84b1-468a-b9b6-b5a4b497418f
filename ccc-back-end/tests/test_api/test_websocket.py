#!/usr/bin/env python3
"""
Simple WebSocket test script to verify the chat endpoint is working.
"""
import asyncio
import websockets
import sys

async def test_websocket():
    # Use a dummy token for testing (this will fail auth but should show if routing works)
    uri = "ws://localhost:8000/chat?token=dummy_token"
    
    try:
        print(f"Connecting to {uri}...")
        async with websockets.connect(uri) as websocket:
            print("Connected successfully!")
            
            # Send a test message
            await websocket.send("Hello, this is a test message")
            print("Message sent")
            
            # Wait for response
            response = await websocket.recv()
            print(f"Received: {response}")
            
    except websockets.exceptions.ConnectionClosedError as e:
        print(f"Connection closed: {e}")
        print("This is expected if token authentication fails")
    except Exception as e:
        print(f"Connection error: {e}")
        print("Error type:", type(e).__name__)

if __name__ == "__main__":
    asyncio.run(test_websocket()) 