import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch, ANY
from app.main import app
from app.db import models
from app.core.security import get_current_user
from app.db.session import get_db
from app.services.cosmos_client import get_profile_cosmos_service
import uuid
from datetime import datetime, UTC


@pytest.fixture
def mock_coach():
    """Mock coach user for testing."""
    coach = models.Coach(
        id=uuid.uuid4(),
        firebase_uid="test_firebase_uid",
        email="<EMAIL>",
        full_name="Test Coach",
        created_at=datetime.now(UTC),
        is_active=True
    )
    return coach


@pytest.fixture
def mock_clients():
    """Mock clients for testing."""
    coach_id = uuid.uuid4()
    clients = [
        models.Client(
            id=uuid.uuid4(),
            coach_id=coach_id,
            cosmos_user_id=uuid.uuid4(),
            cosmos_profile_id=uuid.uuid4(),
            client_full_name="Client One",
            added_at=datetime.now(UTC)
        ),
        models.Client(
            id=uuid.uuid4(),
            coach_id=coach_id,
            cosmos_user_id=uuid.uuid4(),
            cosmos_profile_id=uuid.uuid4(),
            client_full_name="Client Two",
            added_at=datetime.now(UTC)
        )
    ]
    return clients


@pytest.fixture
def mock_profile():
    """Mock Cosmos profile data."""
    return {
        "id": str(uuid.uuid4()),
        "personalInfo": {
            "firstName": "John",
            "lastName": "Doe",
            "email": "<EMAIL>"
        },
        "preferences": {
            "jobSearchStatus": "actively_looking",
            "preferredIndustries": ["Technology", "Finance"]
        },
        "experience": {
            "currentRole": "Software Engineer",
            "yearsOfExperience": 5
        }
    }


class TestDashboardAPI:
    """Test cases for dashboard API endpoints."""

    def test_get_my_clients_success(self, mock_coach, mock_clients, mock_profile):
        """Test successful retrieval of coach's clients."""
        # Mock database session
        mock_db = MagicMock()
        
        # Mock profile service
        mock_profile_service = AsyncMock()
        mock_profile_service.get_profile.return_value = mock_profile
        
        # Override dependencies
        def override_get_current_user():
            return mock_coach
            
        def override_get_db():
            return mock_db
            
        def override_get_profile_cosmos_service():
            return mock_profile_service
        
        app.dependency_overrides[get_current_user] = override_get_current_user
        app.dependency_overrides[get_db] = override_get_db
        app.dependency_overrides[get_profile_cosmos_service] = override_get_profile_cosmos_service
        
        try:
            with patch('app.db.crud.get_clients_by_coach', return_value=mock_clients):
                client = TestClient(app)
                response = client.get("/dashboard/my-clients")
                
                # Assertions
                assert response.status_code == 200
                data = response.json()
                assert len(data) == 2
                
                # Check first client
                client_data = data[0]
                assert "client_id" in client_data
                assert "cosmos_user_id" in client_data
                assert "cosmos_profile_id" in client_data
                assert "client_full_name" in client_data
                assert "added_at" in client_data
                assert "profile" in client_data
                assert client_data["profile"] == mock_profile
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()

    def test_get_my_clients_no_profile_found(self, mock_coach, mock_clients):
        """Test retrieval when profile is not found in Cosmos DB."""
        # Mock database session
        mock_db = MagicMock()
        
        # Mock profile service to return None
        mock_profile_service = AsyncMock()
        mock_profile_service.get_profile.return_value = None
        
        # Override dependencies
        def override_get_current_user():
            return mock_coach
            
        def override_get_db():
            return mock_db
            
        def override_get_profile_cosmos_service():
            return mock_profile_service
        
        app.dependency_overrides[get_current_user] = override_get_current_user
        app.dependency_overrides[get_db] = override_get_db
        app.dependency_overrides[get_profile_cosmos_service] = override_get_profile_cosmos_service
        
        try:
            with patch('app.db.crud.get_clients_by_coach', return_value=mock_clients):
                client = TestClient(app)
                response = client.get("/dashboard/my-clients")
                
                # Assertions
                assert response.status_code == 200
                data = response.json()
                assert len(data) == 2
                
                # Check that profile is None when not found
                for client_data in data:
                    assert client_data["profile"] is None
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()

    def test_get_my_clients_empty_list(self, mock_coach):
        """Test retrieval when coach has no clients."""
        # Mock database session
        mock_db = MagicMock()
        
        # Mock profile service
        mock_profile_service = AsyncMock()
        
        # Override dependencies
        def override_get_current_user():
            return mock_coach
            
        def override_get_db():
            return mock_db
            
        def override_get_profile_cosmos_service():
            return mock_profile_service
        
        app.dependency_overrides[get_current_user] = override_get_current_user
        app.dependency_overrides[get_db] = override_get_db
        app.dependency_overrides[get_profile_cosmos_service] = override_get_profile_cosmos_service
        
        try:
            with patch('app.db.crud.get_clients_by_coach', return_value=[]):
                client = TestClient(app)
                response = client.get("/dashboard/my-clients")
                
                # Assertions
                assert response.status_code == 200
                data = response.json()
                assert len(data) == 0
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()

    def test_get_my_clients_with_pagination(self, mock_coach, mock_clients):
        """Test retrieval with pagination parameters."""
        # Mock database session
        mock_db = MagicMock()
        
        # Mock profile service
        mock_profile_service = AsyncMock()
        
        # Override dependencies
        def override_get_current_user():
            return mock_coach
            
        def override_get_db():
            return mock_db
            
        def override_get_profile_cosmos_service():
            return mock_profile_service
        
        app.dependency_overrides[get_current_user] = override_get_current_user
        app.dependency_overrides[get_db] = override_get_db
        app.dependency_overrides[get_profile_cosmos_service] = override_get_profile_cosmos_service
        
        try:
            with patch('app.db.crud.get_clients_by_coach', return_value=mock_clients[:1]) as mock_get_clients:
                client = TestClient(app)
                response = client.get("/dashboard/my-clients?skip=0&limit=1")
                
                # Assertions
                assert response.status_code == 200
                data = response.json()
                assert len(data) == 1
                
                # Verify that crud.get_clients_by_coach was called with correct parameters
                mock_get_clients.assert_called_once_with(
                    mock_db,
                    coach_id=mock_coach.id,
                    skip=0,
                    limit=1
                )
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()

    def test_get_my_clients_database_error(self, mock_coach):
        """Test handling of database errors."""
        # Mock database session
        mock_db = MagicMock()
        
        # Mock profile service
        mock_profile_service = AsyncMock()
        
        # Override dependencies
        def override_get_current_user():
            return mock_coach
            
        def override_get_db():
            return mock_db
            
        def override_get_profile_cosmos_service():
            return mock_profile_service
        
        app.dependency_overrides[get_current_user] = override_get_current_user
        app.dependency_overrides[get_db] = override_get_db
        app.dependency_overrides[get_profile_cosmos_service] = override_get_profile_cosmos_service
        
        try:
            with patch('app.db.crud.get_clients_by_coach', side_effect=Exception("Database error")):
                client = TestClient(app)
                response = client.get("/dashboard/my-clients")
                
                # Assertions
                assert response.status_code == 500
                assert "Internal server error" in response.json()["detail"]
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()

    def test_add_client_from_talent_pool_success(self, mock_coach):
        """Test successfully adding a client from talent pool."""
        # Mock database session
        mock_db = MagicMock()
        
        # Mock profile data with userId
        mock_profile = {
            "id": str(uuid.uuid4()),
            "userId": str(uuid.uuid4()),
            "personalInfo": {
                "firstName": "Jane",
                "lastName": "Smith",
                "email": "<EMAIL>"
            }
        }
        
        # Mock profile service
        mock_profile_service = AsyncMock()
        mock_profile_service.get_profile.return_value = mock_profile
        
        # Mock created client
        cosmos_profile_id = uuid.uuid4()
        mock_created_client = models.Client(
            id=uuid.uuid4(),
            coach_id=mock_coach.id,
            cosmos_user_id=uuid.uuid4(),
            cosmos_profile_id=cosmos_profile_id,
            client_full_name="Jane Smith",
            added_at=datetime.now(UTC)
        )
        
        # Override dependencies
        def override_get_current_user():
            return mock_coach
            
        def override_get_db():
            return mock_db
            
        def override_get_profile_cosmos_service():
            return mock_profile_service
        
        app.dependency_overrides[get_current_user] = override_get_current_user
        app.dependency_overrides[get_db] = override_get_db
        app.dependency_overrides[get_profile_cosmos_service] = override_get_profile_cosmos_service
        
        try:
            with patch('app.db.crud.get_client_by_cosmos_profile_id', return_value=None), \
                 patch('app.db.crud.create_client', return_value=mock_created_client):
                
                client = TestClient(app)
                response = client.post(
                    "/dashboard/add-client",
                    json={"cosmos_profile_id": str(cosmos_profile_id)}
                )
                
                # Assertions
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert data["message"] == "Client added successfully"
                assert "client" in data
                assert data["client"]["client_full_name"] == "Jane Smith"
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()

    def test_add_client_profile_not_found(self, mock_coach):
        """Test adding client when profile doesn't exist in talent pool."""
        # Mock database session
        mock_db = MagicMock()
        
        # Mock profile service to return None
        mock_profile_service = AsyncMock()
        mock_profile_service.get_profile.return_value = None
        
        # Override dependencies
        def override_get_current_user():
            return mock_coach
            
        def override_get_db():
            return mock_db
            
        def override_get_profile_cosmos_service():
            return mock_profile_service
        
        app.dependency_overrides[get_current_user] = override_get_current_user
        app.dependency_overrides[get_db] = override_get_db
        app.dependency_overrides[get_profile_cosmos_service] = override_get_profile_cosmos_service
        
        try:
            client = TestClient(app)
            cosmos_profile_id = uuid.uuid4()
            response = client.post(
                "/dashboard/add-client",
                json={"cosmos_profile_id": str(cosmos_profile_id)}
            )
            
            # Assertions
            assert response.status_code == 404
            data = response.json()
            assert data["detail"] == "Profile not found in talent pool"
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear()

    def test_add_client_already_exists(self, mock_coach):
        """Test adding client when person is already a client."""
        # Mock database session
        mock_db = MagicMock()
        
        # Mock profile data
        mock_profile = {
            "id": str(uuid.uuid4()),
            "userId": str(uuid.uuid4()),
            "personalInfo": {
                "firstName": "Jane",
                "lastName": "Smith"
            }
        }
        
        # Mock profile service
        mock_profile_service = AsyncMock()
        mock_profile_service.get_profile.return_value = mock_profile
        
        # Mock existing client
        existing_client = models.Client(
            id=uuid.uuid4(),
            coach_id=mock_coach.id,
            cosmos_user_id=uuid.uuid4(),
            cosmos_profile_id=uuid.uuid4(),
            client_full_name="Jane Smith",
            added_at=datetime.now(UTC)
        )
        
        # Override dependencies
        def override_get_current_user():
            return mock_coach
            
        def override_get_db():
            return mock_db
            
        def override_get_profile_cosmos_service():
            return mock_profile_service
        
        app.dependency_overrides[get_current_user] = override_get_current_user
        app.dependency_overrides[get_db] = override_get_db
        app.dependency_overrides[get_profile_cosmos_service] = override_get_profile_cosmos_service
        
        try:
            with patch('app.db.crud.get_client_by_cosmos_profile_id', return_value=existing_client):
                client = TestClient(app)
                cosmos_profile_id = uuid.uuid4()
                response = client.post(
                    "/dashboard/add-client",
                    json={"cosmos_profile_id": str(cosmos_profile_id)}
                )
                
                # Assertions
                assert response.status_code == 400
                data = response.json()
                assert data["detail"] == "This person is already your client"
        finally:
            # Clean up dependency overrides
            app.dependency_overrides.clear() 