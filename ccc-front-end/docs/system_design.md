## **Career Coach Central - Frontend Architecture & Design Specification**

### **1. Overview**

#### **1.1. Product Vision**
To transition Career Coach Central from a data-heavy dashboard to a streamlined, **conversation-driven command center**. The primary user interaction will be a direct dialogue with an AI agent, simplifying workflows and enhancing productivity.

#### **1.2. Core Design Principles**
*   **Chat-First:** The chat interface is not a feature; it is the application. All major actions and data retrieval will be initiated through conversation.
*   **Real-time Feedback:** The user must always be aware of the system's state. We will use visual cues to show when the AI is "thinking," processing, or has completed a task.
*   **Simplicity & Focus:** The UI will be minimalist, removing all non-essential elements to keep the user's focus on the conversation and the insights generated.

#### **1.3. Technology Stack**
*   **Framework:** Next.js 13+ (App Router or Pages Router, examples will use Pages Router for clarity)
*   **Authentication:** Firebase Authentication (Client-side SDK, Google Provider)
*   **State Management:** Zustand
*   **API Communication (HTTP):** React Query (TanStack Query)
*   **API Communication (Real-time):** Native WebSocket API (or `react-use-websocket`)
*   **Styling:** Tailwind CSS (Recommended for rapid, utility-first styling)

---

### **2. Project Setup & Configuration**

#### **2.1. Project Initialization**
Initialize a new Next.js project with Tailwind CSS.

```bash
npx create-next-app@latest career-coach-central-frontend --typescript --tailwind --eslint
```

#### **2.2. Folder Structure**
Create the following directory structure within the `src/` folder to maintain organization and scalability.

```
/src
├── /api/             # (Optional) Next.js API routes for backend-for-frontend patterns
├── /components/      # Reusable React components
│   ├── /chat/        # Components related to the chat interface
│   ├── /common/      # Shared components like Button, Spinner, Card
│   └── /layout/      # Layout components (e.g., DashboardLayout)
├── /hooks/           # Custom React hooks
│   └── useChatSocket.ts
├── /lib/             # Core logic, API clients, and external service initializations
│   ├── apiClient.ts
│   └── firebase.ts
├── /pages/           # Next.js page routes
│   ├── _app.tsx
│   ├── _document.tsx
│   ├── index.tsx
│   ├── login.tsx
│   ├── dashboard.tsx
│   └── /report/
│       └── [reportId].tsx
├── /store/           # Zustand state management stores
│   ├── useAuthStore.ts
│   └── useChatStore.ts
└── /types/           # TypeScript type definitions
    └── index.ts
```

#### **2.3. Environment Variables**
Create a `.env.local` file in the project root. **This file should not be committed to version control.**

```.env.local
# Firebase Configuration (get from your Firebase project console)
NEXT_PUBLIC_FIREBASE_API_KEY=...
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=...
NEXT_PUBLIC_FIREBASE_PROJECT_ID=...
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=...
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=...
NEXT_PUBLIC_FIREBASE_APP_ID=...

# Backend API Endpoints
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000 # Example for local dev
NEXT_PUBLIC_WSS_BASE_URL=ws://localhost:8000   # Example for local dev
```

#### **2.4. Core Libraries Installation**

```bash
# Core Functionality
npm install zustand @tanstack/react-query firebase axios

# Optional but Recommended
npm install react-hot-toast # For notifications
npm install clsx tailwind-merge # For utility class management
npm install lucide-react # For icons
```

---

### **3. Implementation Plan: Step-by-Step**

#### **Phase 1: Authentication Flow**

**Task 3.1: Firebase Client Setup**
*   **File:** `src/lib/firebase.ts`
*   **Action:** Initialize the Firebase app using the environment variables.

```typescript
// src/lib/firebase.ts
import { initializeApp, getApps, getApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();
```

**Task 3.2: Authentication State (Zustand)**
*   **File:** `src/store/useAuthStore.ts`
*   **Action:** Create a store to manage the user's session token and authentication status.

```typescript
// src/store/useAuthStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AuthState {
  sessionToken: string | null;
  setSessionToken: (token: string | null) => void;
  isAuthenticated: boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      sessionToken: null,
      isAuthenticated: false,
      setSessionToken: (token) => set({ sessionToken: token, isAuthenticated: !!token }),
    }),
    {
      name: 'auth-storage', // Key in localStorage
    }
  )
);
```

**Task 3.3: API Client with Auth Interceptor**
*   **File:** `src/lib/apiClient.ts`
*   **Action:** Create a centralized Axios instance that automatically attaches the session token to every request.

```typescript
// src/lib/apiClient.ts
import axios from 'axios';
import { useAuthStore } from '../store/useAuthStore';

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
});

apiClient.interceptors.request.use((config) => {
  const token = useAuthStore.getState().sessionToken;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default apiClient;
```

**Task 3.4: Login Page**
*   **File:** `src/pages/login.tsx`
*   **Action:** Implement the UI and logic for the Google sign-in process.

```typescript
// src/pages/login.tsx
import { signInWithPopup } from 'firebase/auth';
import { auth, googleProvider } from '../lib/firebase';
import apiClient from '../lib/apiClient';
import { useAuthStore } from '../store/useAuthStore';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

export default function LoginPage() {
  const { setSessionToken, isAuthenticated } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  const handleLogin = async () => {
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const idToken = await result.user.getIdToken();
      
      const response = await apiClient.post('/auth/login', {}, {
        headers: { Authorization: `Bearer ${idToken}` }
      });

      const { session_token } = response.data;
      setSessionToken(session_token);
      router.push('/dashboard');
    } catch (error) {
      console.error("Login failed:", error);
      // Add user feedback, e.g., a toast notification
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-900">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-8">Career Coach Central</h1>
        <button onClick={handleLogin} className="...">
          Sign in with Google
        </button>
      </div>
    </div>
  );
}
```

**Task 3.5: Protected Route Logic**
*   **File:** `src/pages/_app.tsx` (or a custom layout component)
*   **Action:** Wrap pages to redirect unauthenticated users.

```typescript
// Example of a simple protected page component
// src/components/layout/ProtectedRoute.tsx
import { useAuthStore } from '../../store/useAuthStore';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

export default function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  if (!isAuthenticated) {
    return <div>Loading...</div>; // Or a spinner
  }

  return <>{children}</>;
}

// Then in pages/dashboard.tsx:
// <ProtectedRoute> ... your dashboard content ... </ProtectedRoute>
```

#### **Phase 2: Dashboard & Chat Interface**

**Task 4.1: Chat State (Zustand)**
*   **File:** `src/store/useChatStore.ts`
*   **Action:** Create a store for managing chat messages and connection status.

```typescript
// src/store/useChatStore.ts
import { create } from 'zustand';
import { ChatMessage } from '../types'; // See Appendix for type definition

interface ChatState {
  messages: ChatMessage[];
  connectionStatus: 'connected' | 'disconnected' | 'connecting';
  addMessage: (message: ChatMessage) => void;
  setConnectionStatus: (status: ChatState['connectionStatus']) => void;
}

export const useChatStore = create<ChatState>((set) => ({
  messages: [],
  connectionStatus: 'disconnected',
  addMessage: (message) => set((state) => ({ messages: [...state.messages, message] })),
  setConnectionStatus: (status) => set({ connectionStatus: status }),
}));
```

**Task 4.2: WebSocket Custom Hook**
*   **File:** `src/hooks/useChatSocket.ts`
*   **Action:** Encapsulate all WebSocket logic into a reusable hook.

```typescript
// src/hooks/useChatSocket.ts
import { useEffect, useRef } from 'react';
import { useAuthStore } from '../store/useAuthStore';
import { useChatStore } from '../store/useChatStore';
import { ChatMessage } from '../types';

export function useChatSocket() {
  const socketRef = useRef<WebSocket | null>(null);
  const sessionToken = useAuthStore((state) => state.sessionToken);
  const { addMessage, setConnectionStatus } = useChatStore();

  useEffect(() => {
    if (!sessionToken) return;

    const wssUrl = `${process.env.NEXT_PUBLIC_WSS_BASE_URL}/chat`;
    // Pass token via subprotocol or query param if server supports it.
    // Example with subprotocol: new WebSocket(url, [token])
    // Assuming server handles token in handshake header, which is harder from browser.
    // Let's assume a query param for simplicity in this example.
    const socket = new WebSocket(`${wssUrl}?token=${sessionToken}`);
    socketRef.current = socket;

    setConnectionStatus('connecting');

    socket.onopen = () => setConnectionStatus('connected');
    socket.onclose = () => setConnectionStatus('disconnected');
    socket.onerror = (err) => console.error('WebSocket Error:', err);
    socket.onmessage = (event) => {
      const data: ChatMessage = JSON.parse(event.data);
      addMessage(data);
    };

    return () => {
      socket.close();
      socketRef.current = null;
    };
  }, [sessionToken, addMessage, setConnectionStatus]);

  const sendMessage = (content: string) => {
    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
      const message: ChatMessage = { type: 'user_message', content };
      socketRef.current.send(JSON.stringify(message));
      addMessage(message); // Optimistically add user message to UI
    }
  };

  return { sendMessage };
}
```
*Note: WebSocket authentication from a browser is tricky. The most secure method (custom headers) is not supported. Common workarounds are sending the token as the first message or as a query parameter (as shown above). Ensure your backend supports the chosen method.*

**Task 4.3: Dashboard Page Layout & Components**
*   **File:** `src/pages/dashboard.tsx`
*   **Action:** Assemble the main dashboard UI, integrating the chat window and context panel.

```typescript
// src/pages/dashboard.tsx
import { useChatSocket } from '../hooks/useChatSocket';
import { useChatStore } from '../store/useChatStore';
import { ChatMessage as MessageType } from '../types';
// Import your components: ProtectedRoute, ChatInput, MessageBubble, DashboardSummary, AgentStatus

export default function DashboardPage() {
  useChatSocket(); // Initialize the socket connection
  const messages = useChatStore((state) => state.messages);
  
  return (
    // <ProtectedRoute>
      <div className="grid grid-cols-1 md:grid-cols-3 h-screen bg-gray-900 text-white">
        {/* Main Chat Area */}
        <main className="col-span-2 flex flex-col p-4">
          <div className="flex-grow overflow-y-auto">
            {messages.map((msg, index) => (
              // <MessageBubble key={index} message={msg} />
              <div key={index}>{JSON.stringify(msg)}</div>
            ))}
          </div>
          {/* <ChatInput /> */}
        </main>

        {/* Context Panel */}
        <aside className="col-span-1 bg-gray-800 p-4 border-l border-gray-700">
          {/* <DashboardSummary /> */}
          {/* <AgentStatus /> */}
        </aside>
      </div>
    // </ProtectedRoute>
  );
}
```
*   **`DashboardSummary.tsx`:** Use `useQuery` from React Query to fetch data from `GET /dashboard/summary` via the `apiClient`. Display loading/error states.
*   **`AgentStatus.tsx`:** Render the `active_agents` data from the `DashboardSummary` query.
*   **`ChatInput.tsx`:** A form with an input field and a submit button that calls the `sendMessage` function from the `useChatSocket` hook.
*   **`MessageBubble.tsx`:** A component that takes a `message` prop and renders different styles based on `message.type`. Use `clsx` for conditional class names. It should handle `user_message`, `agent_message`, `status`, and render a clickable link for `report_link`.

#### **Phase 3: Report Page**

**Task 5.1: Dynamic Report Page**
*   **File:** `src/pages/report/[reportId].tsx`
*   **Action:** Create a server-rendered page to display the report HTML.

```typescript
// src/pages/report/[reportId].tsx
import { GetServerSideProps } from 'next';
import apiClient from '../../lib/apiClient';
// Import SendReportButton component

interface ReportPageProps {
  htmlContent: string;
  reportId: string;
  error?: string;
}

export default function ReportPage({ htmlContent, reportId, error }: ReportPageProps) {
  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  return (
    <div>
      <header className="p-4 bg-gray-800 flex justify-between items-center">
        <h1 className="text-xl text-white">Career Development Report</h1>
        {/* <SendReportButton reportId={reportId} /> */}
      </header>
      <div
        className="prose dark:prose-invert max-w-none p-8"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    </div>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { reportId, token } = context.query;

  if (!reportId || !token) {
    return { notFound: true };
  }

  try {
    const response = await apiClient.get(`/report/${reportId}`, {
      params: { token },
    });
    return {
      props: { htmlContent: response.data, reportId: reportId as string },
    };
  } catch (error) {
    return {
      props: {
        htmlContent: '',
        reportId: reportId as string,
        error: 'Failed to load report. It may be invalid or expired.',
      },
    };
  }
};
```

**Task 5.2: Send Report Functionality**
*   **File:** `src/components/report/SendReportButton.tsx`
*   **Action:** Create a button that triggers the send report API endpoint using `useMutation`.

```typescript
// src/components/report/SendReportButton.tsx
import { useMutation } from '@tanstack/react-query';
import apiClient from '../../lib/apiClient';
import toast from 'react-hot-toast';

export function SendReportButton({ reportId }: { reportId: string }) {
  const mutation = useMutation({
    mutationFn: () => apiClient.post(`/report/${reportId}/send`),
    onSuccess: () => {
      toast.success('Report sending process initiated!');
    },
    onError: () => {
      toast.error('Failed to send report.');
    },
  });

  return (
    <button
      onClick={() => mutation.mutate()}
      disabled={mutation.isLoading}
      className="..."
    >
      {mutation.isLoading ? 'Sending...' : 'Send Report to Client'}
    </button>
  );
}
```

---

### **6. Appendix: Type Definitions**

*   **File:** `src/types/index.ts`
*   **Action:** Define shared TypeScript types.

```typescript
// src/types/index.ts
export interface ChatMessage {
  type: 'user_message' | 'agent_message' | 'status' | 'report_link' | 'error';
  content: string;
  url?: string; // For report_link type
}
```