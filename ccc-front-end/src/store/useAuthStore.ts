// src/store/useAuthStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth } from '../lib/firebase';
import { Coach } from '../types';
import { logTokenInfo } from '../lib/tokenUtils';

interface AuthState {
  sessionToken: string | null;
  user: Coach | null;
  firebaseUser: User | null;
  tokenRefreshInterval: NodeJS.Timeout | null;
  setSessionToken: (token: string | null) => void;
  setUser: (user: Coach | null) => void;
  setFirebaseUser: (user: User | null) => void;
  setLoginData: (token: string, user: Coach) => void;
  refreshToken: () => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  initializeAuthListener: () => () => void; // Returns unsubscribe function
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      sessionToken: null,
      user: null,
      firebaseUser: null,
      tokenRefreshInterval: null,
      isAuthenticated: false,
      setSessionToken: (token) => set({ sessionToken: token, isAuthenticated: !!token }),
      setUser: (user) => set({ user }),
      setFirebaseUser: (user) => set({ firebaseUser: user }),
      setLoginData: (token, user) => set({ 
        sessionToken: token, 
        user, 
        isAuthenticated: true 
      }),
      refreshToken: async () => {
        const { firebaseUser } = get();
        if (firebaseUser) {
          try {
            // Force refresh the token from Firebase
            const token = await firebaseUser.getIdToken(true); // true forces refresh
            set({ 
              sessionToken: token,
              isAuthenticated: true 
            });
            console.log('Token refreshed successfully');
            
            // Log new token info in development
            if (process.env.NODE_ENV === 'development') {
              logTokenInfo(token, 'Refreshed Token');
            }
          } catch (error) {
            console.error('Error refreshing token:', error);
            // If refresh fails, logout user
            get().logout();
          }
        }
      },
      logout: () => {
        const { tokenRefreshInterval } = get();
        
        // Clear the token refresh interval
        if (tokenRefreshInterval) {
          clearInterval(tokenRefreshInterval);
        }
        
        set({ 
          sessionToken: null, 
          user: null, 
          firebaseUser: null,
          isAuthenticated: false,
          tokenRefreshInterval: null
        });
      },
      initializeAuthListener: () => {
        const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
          set({ firebaseUser });
          
          if (firebaseUser) {
            try {
              // Always force refresh the token on auth state change
              const token = await firebaseUser.getIdToken(true); // true forces refresh
              set({ 
                sessionToken: token, 
                isAuthenticated: true 
              });
              
              // Clear existing interval if any
              const { tokenRefreshInterval } = get();
              if (tokenRefreshInterval) {
                clearInterval(tokenRefreshInterval);
              }
              
              // Set up token refresh interval (refresh every 50 minutes)
              const interval = setInterval(async () => {
                await get().refreshToken();
              }, 50 * 60 * 1000); // 50 minutes in milliseconds
              
              set({ tokenRefreshInterval: interval });
              
            } catch (error) {
              console.error('Error getting Firebase ID token:', error);
              set({ 
                sessionToken: null, 
                isAuthenticated: false 
              });
            }
          } else {
            // User is signed out
            const { tokenRefreshInterval } = get();
            if (tokenRefreshInterval) {
              clearInterval(tokenRefreshInterval);
            }
            
            set({ 
              sessionToken: null, 
              user: null,
              isAuthenticated: false,
              tokenRefreshInterval: null
            });
          }
        });
        
        return unsubscribe;
      },
    }),
    {
      name: 'auth-storage', // Key in localStorage
      // Don't persist firebaseUser and tokenRefreshInterval as they contain non-serializable data
      partialize: (state) => ({
        sessionToken: state.sessionToken,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);