// src/store/useChatStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ChatMessage } from '../types';

interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
}

interface ChatState {
  currentSessionId: string | null;
  sessions: Record<string, ChatSession>;
  connectionStatus: 'connected' | 'disconnected' | 'connecting';
  streamingStatus: 'idle' | 'streaming';
  
  // Session management
  createSession: () => string;
  setCurrentSession: (sessionId: string) => void;
  getCurrentSession: () => ChatSession | null;
  deleteSession: (sessionId: string) => void;
  getAllSessions: () => ChatSession[];
  
  // Message management (operates on current session)
  addMessage: (message: ChatMessage) => void;
  updateLastMessage: (message: ChatMessage) => void;
  clearCurrentSessionMessages: () => void;
  
  // Status management
  setConnectionStatus: (status: ChatState['connectionStatus']) => void;
  setStreamingStatus: (status: ChatState['streamingStatus']) => void;
}

const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

const generateSessionTitle = (firstMessage?: string): string => {
  if (firstMessage && firstMessage.length > 0) {
    // Use first 30 characters of the first message as title
    return firstMessage.length > 30 
      ? firstMessage.substring(0, 30) + '...'
      : firstMessage;
  }
  return `Chat ${new Date().toLocaleDateString()}`;
};

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      currentSessionId: null,
      sessions: {},
      connectionStatus: 'disconnected',
      streamingStatus: 'idle',

      createSession: () => {
        const sessionId = generateSessionId();
        const newSession: ChatSession = {
          id: sessionId,
          title: `New Chat`,
          messages: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        set((state) => ({
          sessions: {
            ...state.sessions,
            [sessionId]: newSession,
          },
          currentSessionId: sessionId,
        }));

        return sessionId;
      },

      setCurrentSession: (sessionId: string) => {
        const { sessions } = get();
        if (sessions[sessionId]) {
          set({ currentSessionId: sessionId });
        } else {
          console.warn(`Session ${sessionId} not found`);
        }
      },

      getCurrentSession: (): ChatSession | null => {
        const { currentSessionId, sessions } = get();
        return currentSessionId ? sessions[currentSessionId] || null : null;
      },

      deleteSession: (sessionId: string) => {
        set((state) => {
          const { [sessionId]: _, ...remainingSessions } = state.sessions;
          const newCurrentSessionId = state.currentSessionId === sessionId 
            ? null 
            : state.currentSessionId;
          
          return {
            sessions: remainingSessions,
            currentSessionId: newCurrentSessionId,
          };
        });
      },

      getAllSessions: (): ChatSession[] => {
        const { sessions } = get();
        return Object.values(sessions).sort((a, b) => 
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        );
      },

      addMessage: (message: ChatMessage) => {
        const { currentSessionId, sessions } = get();
        
        if (!currentSessionId) {
          console.warn('No current session, cannot add message');
          return;
        }

        const currentSession = sessions[currentSessionId];
        if (!currentSession) {
          console.warn(`Current session ${currentSessionId} not found`);
          return;
        }

        const updatedSession = {
          ...currentSession,
          messages: [...currentSession.messages, message],
          updatedAt: new Date().toISOString(),
          // Update title with first user message if it's still the default title
          title: currentSession.title === 'New Chat' && message.type === 'user_message'
            ? generateSessionTitle(message.content)
            : currentSession.title,
        };

        set((state) => ({
          sessions: {
            ...state.sessions,
            [currentSessionId]: updatedSession,
          },
        }));
      },

      updateLastMessage: (message: ChatMessage) => {
        const { currentSessionId, sessions } = get();
        
        if (!currentSessionId) {
          console.warn('No current session, cannot update message');
          return;
        }

        const currentSession = sessions[currentSessionId];
        if (!currentSession) {
          console.warn(`Current session ${currentSessionId} not found`);
          return;
        }

        const messages = [...currentSession.messages];
        const lastIndex = messages.length - 1;
        
        if (lastIndex >= 0 && messages[lastIndex].id === message.id) {
          messages[lastIndex] = message;
          
          const updatedSession = {
            ...currentSession,
            messages,
            updatedAt: new Date().toISOString(),
          };

          set((state) => ({
            sessions: {
              ...state.sessions,
              [currentSessionId]: updatedSession,
            },
          }));
        }
      },

      clearCurrentSessionMessages: () => {
        const { currentSessionId, sessions } = get();
        
        if (!currentSessionId) {
          console.warn('No current session, cannot clear messages');
          return;
        }

        const currentSession = sessions[currentSessionId];
        if (!currentSession) {
          console.warn(`Current session ${currentSessionId} not found`);
          return;
        }

        const updatedSession = {
          ...currentSession,
          messages: [],
          updatedAt: new Date().toISOString(),
        };

        set((state) => ({
          sessions: {
            ...state.sessions,
            [currentSessionId]: updatedSession,
          },
        }));
      },

      setConnectionStatus: (status) => set({ connectionStatus: status }),
      setStreamingStatus: (status) => set({ streamingStatus: status }),
    }),
    {
      name: 'chat-sessions',
      partialize: (state) => ({
        currentSessionId: state.currentSessionId,
        sessions: state.sessions,
      }),
    }
  )
);