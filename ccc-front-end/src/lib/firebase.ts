// src/lib/firebase.ts
import { initializeApp, getApps, getApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAtqziw6u-jBV5zahpnw6morE-tmqlHvTI",
  authDomain: "careercoachdemo.firebaseapp.com",
  projectId: "careercoachdemo",
  storageBucket: "careercoachdemo.firebasestorage.app",
  messagingSenderId: "542743828533",
  appId: "1:542743828533:web:d5ece38ce98d7d8d68f97c"
};

// Initialize Firebase
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();