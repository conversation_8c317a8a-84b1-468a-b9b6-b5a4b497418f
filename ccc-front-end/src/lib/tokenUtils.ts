export interface TokenInfo {
  isValid: boolean;
  expiresAt: number;
  timeUntilExpiry: number;
  userId?: string;
  email?: string;
}

/**
 * Decode and analyze Firebase ID token
 */
export const analyzeToken = (token: string): TokenInfo | null => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = JSON.parse(atob(parts[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = payload.exp - currentTime;

    return {
      isValid: timeUntilExpiry > 0,
      expiresAt: payload.exp,
      timeUntilExpiry,
      userId: payload.user_id || payload.sub,
      email: payload.email,
    };
  } catch (error) {
    console.error('Error analyzing token:', error);
    return null;
  }
};

/**
 * Check if token needs refresh (expires in less than 5 minutes)
 */
export const shouldRefreshToken = (token: string): boolean => {
  const tokenInfo = analyzeToken(token);
  if (!tokenInfo) return true;
  
  // Refresh if token expires in less than 5 minutes (300 seconds)
  return tokenInfo.timeUntilExpiry < 300;
};

/**
 * Format time until expiry in human readable format
 */
export const formatTimeUntilExpiry = (seconds: number): string => {
  if (seconds <= 0) return 'Expired';
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${remainingSeconds}s`;
};

/**
 * Log token information for debugging
 */
export const logTokenInfo = (token: string, prefix = 'Token Info'): void => {
  const tokenInfo = analyzeToken(token);
  if (tokenInfo) {
    console.log(`${prefix}:`, {
      isValid: tokenInfo.isValid,
      expiresAt: new Date(tokenInfo.expiresAt * 1000).toISOString(),
      timeUntilExpiry: formatTimeUntilExpiry(tokenInfo.timeUntilExpiry),
      userId: tokenInfo.userId,
      email: tokenInfo.email,
    });
  } else {
    console.log(`${prefix}: Invalid token`);
  }
}; 