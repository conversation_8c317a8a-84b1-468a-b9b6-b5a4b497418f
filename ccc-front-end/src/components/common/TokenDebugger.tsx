import { useEffect, useState } from 'react';
import { useAuthStore } from '../../store/useAuthStore';
import { analyzeToken, formatTimeUntilExpiry } from '../../lib/tokenUtils';

interface TokenDebuggerProps {
  show?: boolean;
}

interface TokenInfo {
  isValid: boolean;
  timeUntilExpiry: number;
  email?: string;
  userId?: string;
  expiresAt: number;
}

export const TokenDebugger: React.FC<TokenDebuggerProps> = ({ show = process.env.NODE_ENV === 'development' }) => {
  const { sessionToken, isAuthenticated, refreshToken } = useAuthStore();
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    if (sessionToken) {
      const info = analyzeToken(sessionToken);
      setTokenInfo(info);
    } else {
      setTokenInfo(null);
    }
  }, [sessionToken]);

  const handleManualRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshToken();
    } catch (error) {
      console.error('Manual refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  if (!show || !isAuthenticated) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs font-mono z-50 max-w-sm">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-yellow-400">Token Debug</h3>
        <button
          onClick={handleManualRefresh}
          disabled={isRefreshing}
          className="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs disabled:opacity-50"
        >
          {isRefreshing ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>
      
      {tokenInfo ? (
        <div className="space-y-1">
          <div className={`font-semibold ${tokenInfo.isValid ? 'text-green-400' : 'text-red-400'}`}>
            Status: {tokenInfo.isValid ? 'Valid' : 'Expired'}
          </div>
          <div>
            Expires: {formatTimeUntilExpiry(tokenInfo.timeUntilExpiry)}
          </div>
          <div>
            User: {tokenInfo.email || tokenInfo.userId || 'Unknown'}
          </div>
          <div>
            Expires At: {new Date(tokenInfo.expiresAt * 1000).toLocaleTimeString()}
          </div>
        </div>
      ) : (
        <div className="text-red-400">No token available</div>
      )}
    </div>
  );
}; 