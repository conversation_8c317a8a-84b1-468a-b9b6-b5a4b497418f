// src/components/chat/MessageBubble.tsx
import { ChatMessage } from '../../types';
import clsx from 'clsx';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import 'highlight.js/styles/github-dark.css';

export default function MessageBubble({ message }: { message: ChatMessage }) {
  const isUser = message.type === 'user_message';
  const isStatus = message.type === 'status';
  const isError = message.type === 'error';
  
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  // Format agent names like career_coach_agent to Career Coach Agent
  const formatAgentName = (agentName: string): string => {
    if (!agentName || agentName === 'user' || agentName === 'system') {
      return agentName;
    }
    
    return agentName
      .replace(/_/g, ' ')  // Replace underscores with spaces
      .replace(/\b\w/g, (char) => char.toUpperCase())  // Capitalize first letter of each word
      .replace(/\s+/g, ' ')  // Normalize multiple spaces to single space
      .trim();
  };

  
  return (
    <div className={clsx('flex items-start space-x-3 animate-slide-up', { 
      'justify-end': isUser, 
      'justify-start': !isUser,
      'justify-center': isStatus
    })}>
      {!isUser && !isStatus && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 rounded-lg gradient-accent flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
        </div>
      )}
      
      <div className={clsx(
        'rounded-2xl px-4 py-3 shadow-lg relative',
        {
          'gradient-accent text-white ml-auto max-w-lg': isUser,
          'glass-card text-slate-100 max-w-4xl': !isUser && !isStatus,
          'bg-slate-800/50 text-slate-300 text-sm mx-auto px-3 py-2 max-w-md': isStatus,
          'bg-red-900/50 text-red-200 max-w-lg': isError
        }
      )}>
        {/* Streaming indicator for partial messages */}
        {message.partial && (
          <div className="absolute -top-1 -right-1">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
          </div>
        )}

        {/* Status messages */}
        {isStatus && (
          <div className="flex items-center space-x-2">
            <span>{message.content}</span>
          </div>
        )}

        {/* Regular messages with markdown support */}
        {!isStatus && (
          <div className="prose prose-sm prose-invert max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeHighlight]}
              components={{
                // Customize code blocks
                code: ({ className, children, ...props }) => {
                  const match = /language-(\w+)/.exec(className || '');
                  const isInline = !className || !match;
                  return isInline ? (
                    <code className="bg-slate-800/50 px-1 py-0.5 rounded text-sm" {...props}>
                      {children}
                    </code>
                  ) : (
                    <pre className="bg-slate-900/50 rounded-lg p-3 my-3 overflow-x-auto">
                      <code className={className} {...props}>
                        {children}
                      </code>
                    </pre>
                  );
                },
                // Customize tables
                table: ({ children, ...props }) => (
                  <div className="overflow-x-auto my-4">
                    <table className="min-w-full border-collapse border border-slate-700" {...props}>
                      {children}
                    </table>
                  </div>
                ),
                th: ({ children, ...props }) => (
                  <th className="border border-slate-700 px-3 py-2 bg-slate-800/50 text-left font-semibold" {...props}>
                    {children}
                  </th>
                ),
                td: ({ children, ...props }) => (
                  <td className="border border-slate-700 px-3 py-2" {...props}>
                    {children}
                  </td>
                ),
                // Customize links
                a: ({ children, href, ...props }) => (
                  <a 
                    href={href} 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="text-blue-400 hover:text-blue-300 underline"
                    {...props}
                  >
                    {children}
                  </a>
                ),
                // Customize headings
                h1: ({ children, ...props }) => (
                  <h1 className="text-xl font-bold mt-4 mb-2 border-b border-slate-700 pb-1" {...props}>
                    {children}
                  </h1>
                ),
                h2: ({ children, ...props }) => (
                  <h2 className="text-lg font-semibold mt-3 mb-2" {...props}>
                    {children}
                  </h2>
                ),
                h3: ({ children, ...props }) => (
                  <h3 className="text-base font-medium mt-2 mb-1" {...props}>
                    {children}
                  </h3>
                ),
                // Customize lists
                ul: ({ children, ...props }) => (
                  <ul className="list-disc list-inside space-y-1 my-2" {...props}>
                    {children}
                  </ul>
                ),
                ol: ({ children, ...props }) => (
                  <ol className="list-decimal list-inside space-y-1 my-2" {...props}>
                    {children}
                  </ol>
                ),
                // Customize blockquotes
                blockquote: ({ children, ...props }) => (
                  <blockquote className="border-l-4 border-slate-600 pl-4 italic my-3 text-slate-300" {...props}>
                    {children}
                  </blockquote>
                ),
              }}
            >
              {message.content}
            </ReactMarkdown>
          </div>
        )}
        
        {/* Report link for report_link type messages */}
        {message.type === 'report_link' && message.url && (
          <div className="mt-3 pt-3 border-t border-white/20">
            <a 
              href={message.url} 
              target="_blank" 
              rel="noopener noreferrer" 
              className="inline-flex items-center text-sm font-medium hover:underline transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              View Report
              <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </a>
          </div>
        )}

        {/* Timestamp */}
        <div className={clsx('text-xs mt-2 opacity-60', {
          'text-right': isUser,
          'text-left': !isUser && !isStatus,
          'text-center': isStatus
        })}>
          {formatTimestamp(message.timestamp)}
          {message.author && message.author !== 'user' && message.author !== 'system' && (
            <span className="ml-2">• {formatAgentName(message.author)}</span>
          )}
        </div>
      </div>
      
      {isUser && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 rounded-lg bg-slate-700 flex items-center justify-center">
            <svg className="w-4 h-4 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        </div>
      )}
    </div>
  );
}