import { useState } from 'react';
import { useChatStore } from '../../store/useChatStore';
import clsx from 'clsx';

interface ChatSessionSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ChatSessionSidebar({ isOpen, onClose }: ChatSessionSidebarProps) {
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  
  const {
    getAllSessions,
    createSession,
    setCurrentSession,
    deleteSession,
    currentSessionId,
    clearCurrentSessionMessages,
  } = useChatStore();

  const sessions = getAllSessions();

  const handleNewSession = () => {
    createSession();
    onClose();
  };

  const handleSelectSession = (sessionId: string) => {
    setCurrentSession(sessionId);
    onClose();
  };

  const handleDeleteSession = async (sessionId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setIsDeleting(sessionId);
    
    // Add a small delay for UX feedback
    setTimeout(() => {
      deleteSession(sessionId);
      setIsDeleting(null);
    }, 200);
  };

  const handleClearCurrentSession = () => {
    clearCurrentSessionMessages();
    onClose();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return 'Today';
    } else if (diffDays === 2) {
      return 'Yesterday';
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 z-40 lg:hidden"
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div className={clsx(
        'fixed lg:absolute top-0 left-0 h-full w-80 glass-card border-r border-slate-700/50 z-50 transition-transform duration-300',
        'flex flex-col',
        isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700/50">
          <h3 className="text-lg font-semibold text-white">Chat Sessions</h3>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700/50 rounded-lg transition-colors lg:hidden"
          >
            <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* New Session Button */}
        <div className="p-4 border-b border-slate-700/50">
          <button
            onClick={handleNewSession}
            className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white rounded-xl transition-all duration-200 group"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span className="font-medium">New Chat</span>
          </button>
        </div>

        {/* Current Session Actions */}
        {currentSessionId && (
          <div className="p-4 border-b border-slate-700/50">
            <button
              onClick={handleClearCurrentSession}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 rounded-lg transition-colors duration-200"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              <span className="text-sm">Clear Current Chat</span>
            </button>
          </div>
        )}

        {/* Sessions List */}
        <div className="flex-1 overflow-y-auto p-4">
          {sessions.length === 0 ? (
            <div className="text-center py-8">
              <svg className="w-12 h-12 text-slate-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <p className="text-slate-400 text-sm">No chat sessions yet</p>
              <p className="text-slate-500 text-xs mt-1">Start a new chat to begin</p>
            </div>
          ) : (
            <div className="space-y-2">
              {sessions.map((session) => (
                <div
                  key={session.id}
                  onClick={() => handleSelectSession(session.id)}
                  className={clsx(
                    'group relative p-3 rounded-xl cursor-pointer transition-all duration-200',
                    'hover:bg-slate-700/30',
                    currentSessionId === session.id
                      ? 'bg-indigo-500/20 border border-indigo-500/30'
                      : 'bg-slate-800/30',
                    isDeleting === session.id && 'animate-pulse bg-red-500/20'
                  )}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className={clsx(
                        'font-medium text-sm truncate',
                        currentSessionId === session.id ? 'text-white' : 'text-slate-300'
                      )}>
                        {session.title}
                      </h4>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-slate-500">
                          {session.messages.length} messages
                        </p>
                        <p className="text-xs text-slate-500">
                          {formatDate(session.updatedAt)}
                        </p>
                      </div>
                    </div>
                    
                    {/* Delete Button */}
                    <button
                      onClick={(e) => handleDeleteSession(session.id, e)}
                      className="opacity-0 group-hover:opacity-100 ml-2 p-1 hover:bg-red-500/20 text-red-400 hover:text-red-300 rounded transition-all duration-200"
                      disabled={isDeleting === session.id}
                    >
                      {isDeleting === session.id ? (
                        <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"/>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                        </svg>
                      ) : (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      )}
                    </button>
                  </div>
                  
                  {/* Current Session Indicator */}
                  {currentSessionId === session.id && (
                    <div className="absolute left-0 top-0 bottom-0 w-1 bg-indigo-500 rounded-l-xl" />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
}