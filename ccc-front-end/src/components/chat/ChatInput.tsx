// src/components/chat/ChatInput.tsx
import { useState, useRef, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useChatStream } from '../../hooks/useChatStream';
import apiClient from '../../lib/apiClient';
import { Client } from '../../types';

interface ClientMention {
  id: string;
  name: string;
  profile?: {
    email?: string;
    job_search_status?: string;
  };
}

export default function ChatInput() {
  const [input, setInput] = useState('');
  const [showClientSuggestions, setShowClientSuggestions] = useState(false);
  const [clientSearchQuery, setClientSearchQuery] = useState('');
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(0);
  const [mentionStartIndex, setMentionStartIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const { sendMessage } = useChatStream();

  // Fetch clients for @client mentions
  const { data: clients = [] } = useQuery<Client[], Error>({
    queryKey: ['myClients', 0, 50], // Get up to 50 clients for mentions
    queryFn: () => apiClient.get('/dashboard/my-clients?skip=0&limit=50').then(res => res.data),
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  // Convert clients to mention format
  const clientMentions: ClientMention[] = clients.map(client => ({
    id: client.client_id,
    name: client.client_full_name,
    profile: client.profile ? {
      email: client.profile.email,
      job_search_status: client.profile.job_search_status
    } : undefined
  }));

  // Filter clients based on search query
  const filteredClients = clientMentions.filter(client =>
    client.name.toLowerCase().includes(clientSearchQuery.toLowerCase())
  );

  // Helper function to escape strings for use in a regular expression
  const escapeRegex = (string: string) => {
    return string.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
  };

  // Extract client IDs from message content
  const extractClientIds = (content: string): string[] => {
    const clientIds: string[] = [];
    const mentionedNames: string[] = [];

    // Sort clients by name length (descending) to match longer names first
    const sortedClients = [...clientMentions].sort((a, b) => b.name.length - a.name.length);

    for (const client of sortedClients) {
      // Avoid matching a shorter name if a longer name containing it has already been matched
      if (mentionedNames.some(mentioned => mentioned.includes(client.name))) {
        continue;
      }

      // Regex to find "@client:Client Name" or "@ Client Name", ensuring it's a whole word
      const mentionPattern = new RegExp(`@(?:client:)?\\s*${escapeRegex(client.name)}\\b`, 'gi');
      
      if (mentionPattern.test(content)) {
        if (!clientIds.includes(client.id)) {
          clientIds.push(client.id);
          mentionedNames.push(client.name);
        }
      }
    }
    
    return clientIds;
  };

  // Handle input changes and detect @ mentions
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const cursorPosition = e.target.selectionStart || 0;
    
    setInput(value);

    // Find @ symbol before cursor
    let atIndex = -1;
    for (let i = cursorPosition - 1; i >= 0; i--) {
      if (value[i] === '@') {
        // Check if this @ is for client mention (not preceded by alphanumeric)
        if (i === 0 || /\s/.test(value[i - 1])) {
          atIndex = i;
          break;
        }
      }
      if (/\s/.test(value[i]) && value[i-1] !== '@') {
        break;
      }
    }

    if (atIndex !== -1) {
      const afterAt = value.substring(atIndex + 1, cursorPosition);
      
      // Handle @client: format
      if (afterAt.toLowerCase().startsWith('client')) {
        const searchQuery = afterAt.substring(6); // Remove "client" prefix
        setClientSearchQuery(searchQuery);
        setMentionStartIndex(atIndex);
        setShowClientSuggestions(true);
        setSelectedSuggestionIndex(0);
      }
      // Handle @ Name format (space after @)
      else if (afterAt.startsWith(' ') || afterAt === '') {
        const searchQuery = afterAt.replace(/^\s+/, ''); // Remove leading spaces
        setClientSearchQuery(searchQuery);
        setMentionStartIndex(atIndex);
        setShowClientSuggestions(true);
        setSelectedSuggestionIndex(0);
      }
      // Handle direct typing after @ (like @Femi)
      else if (/^[A-Za-z]/.test(afterAt)) {
        setClientSearchQuery(afterAt);
        setMentionStartIndex(atIndex);
        setShowClientSuggestions(true);
        setSelectedSuggestionIndex(0);
      }
      else {
        setShowClientSuggestions(false);
      }
    } else {
      setShowClientSuggestions(false);
    }
  };

  // Handle keyboard navigation in suggestions
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showClientSuggestions) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < filteredClients.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : filteredClients.length - 1
        );
        break;
      case 'Enter':
        if (filteredClients.length > 0) {
          e.preventDefault();
          selectClient(filteredClients[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setShowClientSuggestions(false);
        break;
    }
  };

  // Select a client from suggestions
  const selectClient = (client: ClientMention) => {
    if (mentionStartIndex === -1) return;

    const beforeMention = input.substring(0, mentionStartIndex);
    const afterCursor = input.substring(inputRef.current?.selectionStart || 0);
    
    // Determine mention format based on what's already typed
    const afterAt = input.substring(mentionStartIndex + 1, inputRef.current?.selectionStart || 0);
    let mentionText: string;
    
    if (afterAt.toLowerCase().startsWith('client')) {
      // Use @client:Name format
      mentionText = `@client:${client.name}`;
    } else {
      // Use @ Name format for direct mentions
      mentionText = `@ ${client.name}`;
    }
    
    const newInput = beforeMention + mentionText + ' ' + afterCursor;
    
    setInput(newInput);
    setShowClientSuggestions(false);
    
    // Focus back to input and set cursor position
    setTimeout(() => {
      if (inputRef.current) {
        const newCursorPosition = beforeMention.length + mentionText.length + 1;
        inputRef.current.focus();
        inputRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
      }
    }, 0);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim()) {
      const messageContent = input.trim();
      const clientIds = extractClientIds(messageContent);
      
      // Debug logging
      console.log('Message content:', messageContent);
      console.log('Available clients:', clientMentions.map(c => ({ id: c.id, name: c.name })));
      console.log('Extracted client IDs:', clientIds);
      
      // Send message with extracted client IDs
      sendMessage(messageContent, clientIds);
      setInput('');
      setShowClientSuggestions(false);
    }
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node) &&
          inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setShowClientSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative">
      <form onSubmit={handleSubmit} className="flex items-end space-x-3">
        <div className="flex-1 relative">
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            className="w-full px-4 py-3 pr-12 rounded-xl glass-card text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 transition-all duration-200"
            placeholder="Type your message... (use @client to mention clients)"
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
        </div>
        
        <button
          type="submit"
          disabled={!input.trim()}
          className="flex-shrink-0 w-12 h-12 rounded-xl gradient-accent text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-indigo-500/50"
        >
          <svg className="w-5 h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        </button>
      </form>

      {/* Client Suggestions Dropdown */}
      {showClientSuggestions && (
        <div 
          ref={suggestionsRef}
          className="absolute bottom-full left-0 right-0 mb-2 max-h-48 overflow-y-auto glass-card rounded-xl border border-slate-700/50 z-50"
        >
          {filteredClients.length > 0 ? (
            <>
              <div className="px-3 py-2 text-xs text-slate-400 border-b border-slate-700/50">
                Select a client to mention:
              </div>
              {filteredClients.map((client, index) => (
                <button
                  key={client.id}
                  type="button"
                  className={`w-full px-3 py-2 text-left hover:bg-slate-700/50 transition-colors flex items-center justify-between ${
                    index === selectedSuggestionIndex ? 'bg-slate-700/50' : ''
                  }`}
                  onClick={() => selectClient(client)}
                  onMouseEnter={() => setSelectedSuggestionIndex(index)}
                >
                  <div>
                    <div className="text-sm font-medium text-white">{client.name}</div>
                    {client.profile?.email && (
                      <div className="text-xs text-slate-400">{client.profile.email}</div>
                    )}
                  </div>
                  {client.profile?.job_search_status && (
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      client.profile.job_search_status === 'actively_looking' 
                        ? 'bg-green-900/50 text-green-300' 
                        : client.profile.job_search_status === 'open_to_offers'
                        ? 'bg-yellow-900/50 text-yellow-300'
                        : 'bg-slate-900/50 text-slate-300'
                    }`}>
                      {client.profile.job_search_status.replace('_', ' ')}
                    </span>
                  )}
                </button>
              ))}
            </>
          ) : (
            <div className="px-3 py-4 text-sm text-slate-400 text-center">
              No clients found matching &quot;{clientSearchQuery}&quot;
            </div>
          )}
        </div>
      )}
    </div>
  );
}