// src/components/dashboard/DashboardSummary.tsx
import { useQuery } from '@tanstack/react-query';
import apiClient from '../../lib/apiClient';
import { DashboardSummary as DashboardSummaryType } from '../../types';

export default function DashboardSummary() {
  const { data, isLoading, error } = useQuery<DashboardSummaryType, Error>({
    queryKey: ['dashboardSummary'],
    queryFn: () => apiClient.get('/dashboard/summary').then(res => res.data),
    refetchInterval: 30000, // Refetch every 30 seconds for live data
  });

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  return (
    <div className="glass-card rounded-2xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <svg className="w-5 h-5 mr-2 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        Dashboard Overview
      </h3>
      
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
          <span className="ml-3 text-slate-400">Loading dashboard data...</span>
        </div>
      )}
      
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span className="text-red-300 text-sm">Failed to load dashboard data</span>
          </div>
        </div>
      )}
      
      {data && (
        <div className="space-y-4">
          {/* My Clients */}
          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 border border-indigo-500/20 rounded-xl">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-xl bg-indigo-500/20 flex items-center justify-center">
                <svg className="w-6 h-6 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-slate-300 text-sm font-medium">My Clients</p>
                <p className="text-white font-bold text-2xl">{formatNumber(data.my_clients_count)}</p>
              </div>
            </div>
            <div className="text-right">
              <span className="inline-flex items-center text-xs text-indigo-400 bg-indigo-500/10 px-2 py-1 rounded-full">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Active
              </span>
            </div>
          </div>
          
          {/* Global Talent Pool */}
          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-xl">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-xl bg-green-500/20 flex items-center justify-center">
                <svg className="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-slate-300 text-sm font-medium">Global Talent Pool</p>
                <p className="text-white font-bold text-2xl">{formatNumber(data.global_talent_pool_count)}</p>
              </div>
            </div>
            <div className="text-right">
              <span className="inline-flex items-center text-xs text-green-400 bg-green-500/10 px-2 py-1 rounded-full">
                <span className="status-dot status-active"></span>
                Available
              </span>
            </div>
          </div>

          {/* Active Jobs */}
          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-xl">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-xl bg-blue-500/20 flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8m0 0H4a2 2 0 00-2 2v6a2 2 0 002 2h2m2-6h8m-8 0v6a2 2 0 002 2h4a2 2 0 002-2v-6m-8 0V8a2 2 0 012-2h4a2 2 0 012 2v2" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-slate-300 text-sm font-medium">Active Jobs</p>
                <p className="text-white font-bold text-2xl">{formatNumber(data.active_jobs_count)}</p>
              </div>
            </div>
            <div className="text-right">
              <span className="inline-flex items-center text-xs text-blue-400 bg-blue-500/10 px-2 py-1 rounded-full">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                Hiring
              </span>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="bg-slate-800/30 rounded-xl p-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-slate-400 text-xs uppercase tracking-wide">Success Rate</p>
                <p className="text-white font-semibold text-lg">94%</p>
              </div>
              <div>
                <p className="text-slate-400 text-xs uppercase tracking-wide">Avg Response</p>
                <p className="text-white font-semibold text-lg">1.2s</p>
              </div>
              <div>
                <p className="text-slate-400 text-xs uppercase tracking-wide">Uptime</p>
                <p className="text-green-400 font-semibold text-lg">99.9%</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}