import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import apiClient from '../../lib/apiClient';
import { TalentPoolFilters, AddClientRequest, AddClientResponse } from '../../types';
import toast from 'react-hot-toast';

interface TalentPoolProps {
  limit?: number;
  showPagination?: boolean;
  showFilters?: boolean;
  className?: string;
}

interface TalentBasicInfo {
  fullName?: string;
}

interface TalentContactInfo {
  emails?: Array<{
    email: string;
    isPrimary: boolean;
  }>;
  addresses?: Array<{
    city: string;
    stateProvince: string;
  }>;
}

interface TalentPreferences {
  jobSearchStatus?: string;
  preferredIndustries?: string[];
}

interface TalentSkill {
  skillName: string;
}

interface TalentProfile {
  id?: string;
  cosmos_profile_id?: string;
  basicInfo?: TalentBasicInfo;
  contactInfo?: TalentContactInfo;
  preferences?: TalentPreferences;
  skillsAndExpertise?: TalentSkill[];
  full_name?: string;
  email?: string;
  job_search_status?: string;
  preferred_industries?: string[];
  skills?: string[];
  experience_level?: string;
  location?: string;
}

// Helper function to safely get full name from talent profile
const getFullName = (talent: TalentProfile): string => {
  return talent?.basicInfo?.fullName || talent?.full_name || 'Unknown';
};

// Helper function to safely get email from talent profile
const getEmail = (talent: TalentProfile): string => {
  const primaryEmail = talent?.contactInfo?.emails?.find((email) => email.isPrimary);
  return primaryEmail?.email || talent?.email || 'No email';
};

// Helper function to safely get location from talent profile
const getLocation = (talent: TalentProfile): string => {
  const address = talent?.contactInfo?.addresses?.[0];
  if (address) {
    return `${address.city}, ${address.stateProvince}`;
  }
  return talent?.location || '';
};

// Helper function to safely get job search status
const getJobSearchStatus = (talent: TalentProfile): string => {
  return talent?.preferences?.jobSearchStatus || talent?.job_search_status || 'unknown';
};

// Helper function to safely get preferred industries
const getPreferredIndustries = (talent: TalentProfile): string[] => {
  return talent?.preferences?.preferredIndustries || talent?.preferred_industries || [];
};

// Helper function to safely get skills
const getSkills = (talent: TalentProfile): string[] => {
  const skillsFromProfile = talent?.skillsAndExpertise?.map((skill) => skill.skillName) || [];
  return skillsFromProfile.length > 0 ? skillsFromProfile : (talent?.skills || []);
};

// Helper function to get initials safely
const getInitials = (fullName: string): string => {
  if (!fullName || fullName === 'Unknown') return 'UN';
  return fullName.split(' ').map(n => n[0]).join('').slice(0, 2).toUpperCase();
};

// Helper function to get cosmos profile ID
const getCosmosProfileId = (talent: TalentProfile): string => {
  return talent?.id || talent?.cosmos_profile_id || '';
};

export default function TalentPool({ limit = 20, showPagination = true, showFilters = true, className = '' }: TalentPoolProps) {
  const [filters, setFilters] = useState<TalentPoolFilters>({
    skip: 0,
    limit,
    job_search_status: '',
    preferred_industries: []
  });

  const queryClient = useQueryClient();

  const { data: talents = [], isLoading, error, refetch } = useQuery<TalentProfile[], Error>({
    queryKey: ['talentPool', filters],
    queryFn: () => {
      const params = new URLSearchParams();
      params.append('skip', filters.skip?.toString() || '0');
      params.append('limit', filters.limit?.toString() || limit.toString());
      if (filters.job_search_status) {
        params.append('job_search_status', filters.job_search_status);
      }
      if (filters.preferred_industries && filters.preferred_industries.length > 0) {
        filters.preferred_industries.forEach(industry => {
          params.append('preferred_industries', industry);
        });
      }
      return apiClient.get(`/dashboard/talent-pool?${params.toString()}`).then(res => res.data);
    },
    refetchInterval: 60000, // Refetch every minute
  });

  // Mutation for adding talent as client
  const addClientMutation = useMutation<AddClientResponse, Error, string>({
    mutationFn: async (cosmosProfileId: string) => {
      const requestData: AddClientRequest = {
        cosmos_profile_id: cosmosProfileId
      };
      const response = await apiClient.post<AddClientResponse>('/dashboard/add-client', requestData);
      return response.data;
    },
    onSuccess: () => {
      toast.success('Talent added as client successfully!');
      // Optionally refetch clients data
      queryClient.invalidateQueries({ queryKey: ['clients'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardSummary'] });
    },
    onError: (error: Error & { response?: { data?: { detail?: string } } }) => {
      const errorMessage = error.response?.data?.detail || 'Failed to add talent as client';
      if (errorMessage.includes('already your client')) {
        toast.error('This person is already your client');
      } else {
        toast.error(errorMessage);
      }
    },
  });

  const handleAddAsClient = (talent: TalentProfile) => {
    const cosmosProfileId = getCosmosProfileId(talent);
    if (!cosmosProfileId) {
      toast.error('Unable to add talent: Missing profile ID');
      return;
    }
    addClientMutation.mutate(cosmosProfileId);
  };

  // Filter talents based on the search query
  const filteredTalents = talents.filter(talent => {
    const searchQuery = filters.job_search_status?.toLowerCase() || '';
    if (!searchQuery) return true;
    
    const fullName = getFullName(talent).toLowerCase();
    const email = getEmail(talent).toLowerCase();
    const status = getJobSearchStatus(talent).toLowerCase();
    
    return fullName.includes(searchQuery) || 
           email.includes(searchQuery) || 
           status.includes(searchQuery);
  });

  const jobSearchStatuses = [
    { value: '', label: 'All Status' },
    { value: 'actively_looking', label: 'Actively Looking' },
    { value: 'passively_looking', label: 'Passively Looking' },
    { value: 'not_looking', label: 'Not Looking' },
    { value: 'employed', label: 'Employed' }
  ];

  const commonIndustries = [
    'Technology', 'Finance', 'Healthcare', 'Education', 'Marketing',
    'Sales', 'Manufacturing', 'Consulting', 'Media', 'Non-profit'
  ];

  const getJobSearchStatusColor = (status: string) => {
    const normalizedStatus = status?.toLowerCase().replace(/\s+/g, '_');
    switch (normalizedStatus) {
      case 'actively_looking':
        return 'bg-green-500/20 text-green-300 border border-green-500/30';
      case 'passively_looking':
        return 'bg-blue-500/20 text-blue-300 border border-blue-500/30';
      case 'not_looking':
        return 'bg-gray-500/20 text-gray-300 border border-gray-500/30';
      case 'employed':
        return 'bg-purple-500/20 text-purple-300 border border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border border-gray-500/30';
    }
  };

  const getJobSearchStatusText = (status: string) => {
    if (!status) return 'Unknown';
    const normalizedStatus = status.toLowerCase().replace(/\s+/g, '_');
    switch (normalizedStatus) {
      case 'actively_looking':
        return 'Actively Looking';
      case 'passively_looking':
        return 'Passively Looking';
      case 'not_looking':
        return 'Not Looking';
      case 'employed':
        return 'Employed';
      default:
        return status; // Return original status if not recognized
    }
  };

  const handleStatusFilter = (status: string) => {
    setFilters(prev => ({ ...prev, job_search_status: status, skip: 0 }));
  };

  const handleIndustryFilter = (industry: string) => {
    setFilters(prev => {
      const currentIndustries = prev.preferred_industries || [];
      const newIndustries = currentIndustries.includes(industry)
        ? currentIndustries.filter(i => i !== industry)
        : [...currentIndustries, industry];
      return { ...prev, preferred_industries: newIndustries, skip: 0 };
    });
  };

  const handlePrevious = () => {
    setFilters(prev => ({ ...prev, skip: Math.max(0, (prev.skip || 0) - limit) }));
  };

  const handleNext = () => {
    if (talents.length === limit) {
      setFilters(prev => ({ ...prev, skip: (prev.skip || 0) + limit }));
    }
  };

  const clearFilters = () => {
    setFilters({ skip: 0, limit, job_search_status: '', preferred_industries: [] });
  };

  return (
    <div className={`glass-card rounded-2xl p-6 flex flex-col h-full ${className}`}>
      <div className="flex items-center justify-between mb-6 flex-shrink-0">
        <h3 className="text-lg font-semibold text-white flex items-center">
          <svg className="w-5 h-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          Global Talent Pool
        </h3>
        <button
          onClick={() => refetch()}
          className="text-slate-400 hover:text-white transition-colors duration-200"
          title="Refresh talent pool"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="mb-6 space-y-4 flex-shrink-0">
          {/* Job Search Status Filter */}
          <div>
            <p className="text-slate-400 text-sm mb-2">Job Search Status</p>
            <div className="flex flex-wrap gap-2">
              {jobSearchStatuses.map((status) => (
                <button
                  key={status.value}
                  onClick={() => handleStatusFilter(status.value)}
                  className={`px-3 py-1.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                    filters.job_search_status === status.value
                      ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                      : 'bg-slate-700/50 text-slate-400 hover:text-white border border-slate-600/50'
                  }`}
                >
                  {status.label}
                </button>
              ))}
            </div>
          </div>

          {/* Industries Filter */}
          <div>
            <p className="text-slate-400 text-sm mb-2">Industries</p>
            <div className="flex flex-wrap gap-2">
              {commonIndustries.map((industry) => (
                <button
                  key={industry}
                  onClick={() => handleIndustryFilter(industry)}
                  className={`px-3 py-1.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                    filters.preferred_industries?.includes(industry)
                      ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30'
                      : 'bg-slate-700/50 text-slate-400 hover:text-white border border-slate-600/50'
                  }`}
                >
                  {industry}
                </button>
              ))}
            </div>
          </div>

          {/* Clear Filters */}
          {(filters.job_search_status || (filters.preferred_industries && filters.preferred_industries.length > 0)) && (
            <button
              onClick={clearFilters}
              className="text-slate-400 hover:text-white text-sm transition-colors duration-200"
            >
              Clear all filters
            </button>
          )}
        </div>
      )}

      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
          <span className="ml-3 text-slate-400">Loading talent pool...</span>
        </div>
      )}

      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span className="text-red-300 text-sm">Failed to load talent pool</span>
          </div>
        </div>
      )}

      {/* Scrollable Content Area */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {!isLoading && !error && (
          <>
            {talents.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 rounded-full bg-slate-700/50 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <p className="text-slate-400 text-sm">No talent profiles found</p>
                <p className="text-slate-500 text-xs mt-1">Try adjusting your filters or check back later</p>
              </div>
            ) : (
              <div className="h-full overflow-y-auto pr-2 space-y-4">
                {filteredTalents.map((talent) => (
                <div key={talent.id} className="bg-slate-800/30 rounded-xl p-4 border border-slate-700/50 hover:border-slate-600/50 transition-colors duration-200">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500/20 to-emerald-500/20 flex items-center justify-center">
                        <span className="text-white font-semibold text-lg">
                          {getInitials(getFullName(talent))}
                        </span>
                      </div>
                      <div className="flex-1">
                        <h4 className="text-white font-semibold">{getFullName(talent)}</h4>
                        <p className="text-slate-400 text-sm">{getEmail(talent)}</p>
                        {getLocation(talent) && (
                          <p className="text-slate-400 text-sm flex items-center mt-1">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            {getLocation(talent)}
                          </p>
                        )}
                        {talent.experience_level && (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30 mt-2">
                            {talent.experience_level}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="text-right space-y-2">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getJobSearchStatusColor(getJobSearchStatus(talent))}`}>
                        {getJobSearchStatusText(getJobSearchStatus(talent))}
                      </span>
                    </div>
                  </div>
                  
                  {/* Skills */}
                  {getSkills(talent).length > 0 && (
                    <div className="mt-4">
                      <p className="text-slate-400 text-xs mb-2">Skills</p>
                      <div className="flex flex-wrap gap-2">
                        {getSkills(talent).slice(0, 6).map((skill, index) => (
                          <span key={index} className="px-2 py-1 bg-slate-700/50 text-slate-300 text-xs rounded-md">
                            {skill}
                          </span>
                        ))}
                        {getSkills(talent).length > 6 && (
                          <span className="px-2 py-1 bg-slate-700/50 text-slate-400 text-xs rounded-md">
                            +{getSkills(talent).length - 6} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                                    {/* Preferred Industries */}
                  {getPreferredIndustries(talent).length > 0 && (
                    <div className="mt-3">
                      <p className="text-slate-400 text-xs mb-2">Preferred Industries</p>
                      <div className="flex flex-wrap gap-2">
                        {getPreferredIndustries(talent).slice(0, 3).map((industry, index) => (
                          <span key={index} className="px-2 py-1 bg-emerald-500/10 text-emerald-300 text-xs rounded-md border border-emerald-500/20">
                            {industry}
                          </span>
                        ))}
                        {getPreferredIndustries(talent).length > 3 && (
                          <span className="px-2 py-1 bg-slate-700/50 text-slate-400 text-xs rounded-md">
                            +{getPreferredIndustries(talent).length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Add as Client Button */}
                  <div className="mt-4 pt-3 border-t border-slate-700/30 flex justify-end">
                    <button
                      onClick={() => handleAddAsClient(talent)}
                      disabled={addClientMutation.isPending}
                      className="flex items-center px-4 py-2 bg-indigo-500/20 hover:bg-indigo-500/30 text-indigo-300 hover:text-indigo-200 text-sm font-medium rounded-lg border border-indigo-500/30 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {addClientMutation.isPending ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Adding...
                        </>
                      ) : (
                        <>
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                          Add as Client
                        </>
                      )}
                    </button>
                  </div>
                </div>
              ))}
              </div>
            )}
          </>
        )}
      </div>

      {/* Pagination - Fixed at bottom */}
      {!isLoading && !error && showPagination && talents.length > 0 && (
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-slate-700/50 flex-shrink-0">
          <button
            onClick={handlePrevious}
            disabled={(filters.skip || 0) === 0}
            className="flex items-center px-4 py-2 text-sm text-slate-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Previous
          </button>
          
          <span className="text-slate-400 text-sm">
            Showing {(filters.skip || 0) + 1}-{(filters.skip || 0) + talents.length} profiles
          </span>
          
          <button
            onClick={handleNext}
            disabled={talents.length < limit}
            className="flex items-center px-4 py-2 text-sm text-slate-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            Next
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
} 