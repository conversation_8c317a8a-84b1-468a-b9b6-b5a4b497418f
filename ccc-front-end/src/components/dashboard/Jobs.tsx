import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import apiClient from '../../lib/apiClient';
import { Job, JobFilters } from '../../types';

interface JobsProps {
  limit?: number;
  showPagination?: boolean;
  showFilters?: boolean;
  className?: string;
}

// Helper functions for safe data extraction
const getCompanyName = (job: Job): string => {
  return job.organization_info?.name || job.company || 'Unknown Company';
};

const getCompanyLogo = (job: Job): string | undefined => {
  return job.organization_info?.logo_url;
};

const getJobLocation = (job: Job): string => {
  if (job.locations && job.locations.length > 0) {
    return job.locations[0];
  }
  return job.location || job.organization_info?.location || 'Remote';
};

const getJobDescription = (job: Job): string => {
  if (job.summary) {
    // Remove HTML tags from summary
    return job.summary.replace(/<[^>]*>/g, '').trim();
  }
  if (job.job_description) {
    // Remove HTML tags and truncate
    const cleanDesc = job.job_description.replace(/<[^>]*>/g, '').trim();
    return cleanDesc.length > 200 ? cleanDesc.substring(0, 200) + '...' : cleanDesc;
  }
  return job.description || 'No description available';
};

const formatCompensation = (compensation: Job['compensation']): string => {
  if (!compensation || (compensation.min_cents === 0 && compensation.max_cents === 0)) {
    return 'Salary not disclosed';
  }
  
  const formatAmount = (cents: number): string => {
    const amount = cents / 100;
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(0)}K`;
    }
    return amount.toFixed(0);
  };

  const currency = compensation.currency || 'USD';
  const period = compensation.period?.toLowerCase() || 'yearly';
  
  if (compensation.min_cents === compensation.max_cents) {
    return `${currency} ${formatAmount(compensation.min_cents)}/${period}`;
  }
  
  return `${currency} ${formatAmount(compensation.min_cents)} - ${formatAmount(compensation.max_cents)}/${period}`;
};

const getWorkModeIcon = (workMode: string) => {
  switch (workMode) {
    case 'remote':
      return (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
        </svg>
      );
    case 'hybrid':
      return (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      );
    case 'on_site':
    default:
      return (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      );
  }
};

const getSeniorityColor = (seniority: string) => {
  switch (seniority) {
    case 'entry':
      return 'bg-green-500/20 text-green-300 border border-green-500/30';
    case 'junior':
      return 'bg-blue-500/20 text-blue-300 border border-blue-500/30';
    case 'mid':
      return 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30';
    case 'senior':
      return 'bg-orange-500/20 text-orange-300 border border-orange-500/30';
    case 'lead':
      return 'bg-purple-500/20 text-purple-300 border border-purple-500/30';
    case 'executive':
      return 'bg-red-500/20 text-red-300 border border-red-500/30';
    default:
      return 'bg-gray-500/20 text-gray-300 border border-gray-500/30';
  }
};

export default function Jobs({ limit = 20, showPagination = true, showFilters = true, className = '' }: JobsProps) {
  const [filters, setFilters] = useState<JobFilters>({
    skip: 0,
    limit,
    status: 'active',
    company_id: ''
  });

  const { data: jobs = [], isLoading, error, refetch } = useQuery<Job[], Error>({
    queryKey: ['jobs', filters],
    queryFn: () => {
      const params = new URLSearchParams();
      params.append('skip', filters.skip?.toString() || '0');
      params.append('limit', filters.limit?.toString() || limit.toString());
      if (filters.status) {
        params.append('status', filters.status);
      }
      if (filters.company_id) {
        params.append('company_id', filters.company_id);
      }
      return apiClient.get(`/dashboard/jobs?${params.toString()}`).then(res => res.data);
    },
    refetchInterval: 60000, // Refetch every minute
  });

  const jobStatuses = [
    { value: '', label: 'All Status' },
    { value: 'active', label: 'Active' },
    { value: 'closed', label: 'Closed' },
    { value: 'draft', label: 'Draft' },
    { value: 'expired', label: 'Expired' }
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-300 border border-green-500/30';
      case 'closed':
        return 'bg-red-500/20 text-red-300 border border-red-500/30';
      case 'draft':
        return 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30';
      case 'expired':
        return 'bg-gray-500/20 text-gray-300 border border-gray-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border border-gray-500/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'closed':
        return 'Closed';
      case 'draft':
        return 'Draft';
      case 'expired':
        return 'Expired';
      default:
        return 'Unknown';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'closed':
        return (
          <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'draft':
        return (
          <svg className="w-4 h-4 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        );
      case 'expired':
        return (
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const handleStatusFilter = (status: string) => {
    setFilters(prev => ({ ...prev, status, skip: 0 }));
  };

  const handlePrevious = () => {
    setFilters(prev => ({ ...prev, skip: Math.max(0, (prev.skip || 0) - limit) }));
  };

  const handleNext = () => {
    if (jobs.length === limit) {
      setFilters(prev => ({ ...prev, skip: (prev.skip || 0) + limit }));
    }
  };

  const clearFilters = () => {
    setFilters({ skip: 0, limit, status: '', company_id: '' });
  };

  return (
    <div className={`glass-card rounded-2xl p-6 flex flex-col h-full ${className}`}>
      <div className="flex items-center justify-between mb-6 flex-shrink-0">
        <h3 className="text-lg font-semibold text-white flex items-center">
          <svg className="w-5 h-5 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8m0 0H4a2 2 0 00-2 2v6a2 2 0 002 2h2m2-6h8m-8 0v6a2 2 0 002 2h4a2 2 0 002-2v-6m-8 0V8a2 2 0 012-2h4a2 2 0 012 2v2" />
          </svg>
          Job Listings
        </h3>
        <button
          onClick={() => refetch()}
          className="text-slate-400 hover:text-white transition-colors duration-200"
          title="Refresh jobs"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="mb-6 space-y-4 flex-shrink-0">
          {/* Job Status Filter */}
          <div>
            <p className="text-slate-400 text-sm mb-2">Job Status</p>
            <div className="flex flex-wrap gap-2">
              {jobStatuses.map((status) => (
                <button
                  key={status.value}
                  onClick={() => handleStatusFilter(status.value)}
                  className={`px-3 py-1.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                    filters.status === status.value
                      ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30'
                      : 'bg-slate-700/50 text-slate-400 hover:text-white border border-slate-600/50'
                  }`}
                >
                  {status.label}
                </button>
              ))}
            </div>
          </div>

          {/* Clear Filters */}
          {filters.status && (
            <button
              onClick={clearFilters}
              className="text-slate-400 hover:text-white text-sm transition-colors duration-200"
            >
              Clear all filters
            </button>
          )}
        </div>
      )}

      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-slate-400">Loading jobs...</span>
        </div>
      )}

      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span className="text-red-300 text-sm">Failed to load jobs</span>
          </div>
        </div>
      )}

      {/* Scrollable Content Area */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {!isLoading && !error && (
          <>
            {jobs.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 rounded-full bg-slate-700/50 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8m0 0H4a2 2 0 00-2 2v6a2 2 0 002 2h2m2-6h8m-8 0v6a2 2 0 002 2h4a2 2 0 002-2v-6m-8 0V8a2 2 0 012-2h4a2 2 0 012 2v2" />
                  </svg>
                </div>
                <p className="text-slate-400 text-sm">No jobs found</p>
                <p className="text-slate-500 text-xs mt-1">Try adjusting your filters or check back later</p>
              </div>
            ) : (
              <div className="h-full overflow-y-auto pr-2 space-y-4">
                {jobs.map((job) => (
                  <div key={job.id} className="bg-slate-800/30 rounded-xl p-6 border border-slate-700/50 hover:border-slate-600/50 transition-colors duration-200">
                    {/* Header with Company Logo and Job Title */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start space-x-4">
                        <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 flex items-center justify-center overflow-hidden">
                          {getCompanyLogo(job) ? (
                            <img
                              src={getCompanyLogo(job) || ''}
                              alt={`${getCompanyName(job)} logo`}
                              width={64}
                              height={64}
                              className="w-full h-full object-cover"
                            />
                          ) : null}
                          <div className={`w-full h-full flex items-center justify-center ${getCompanyLogo(job) ? 'hidden' : ''}`}>
                            {getStatusIcon(job.status)}
                          </div>
                        </div>
                        <div className="flex-1">
                          <h4 className="text-white font-semibold text-lg mb-1">{job.title}</h4>
                          <p className="text-slate-400 text-sm font-medium mb-2">{getCompanyName(job)}</p>
                          
                          {/* Job Meta Information */}
                          <div className="flex items-center flex-wrap gap-4 text-sm text-slate-400">
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                              </svg>
                              {getJobLocation(job)}
                            </div>
                            
                            <div className="flex items-center">
                              {getWorkModeIcon(job.work_mode)}
                              <span className="ml-1 capitalize">{job.work_mode?.replace('_', ' ') || 'On-site'}</span>
                            </div>
                            
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8m0 0H4a2 2 0 00-2 2v6a2 2 0 002 2h2m2-6h8m-8 0v6a2 2 0 002 2h4a2 2 0 002-2v-6m-8 0V8a2 2 0 012-2h4a2 2 0 012 2v2" />
                              </svg>
                              {job.job_type?.replace('_', ' ') || 'Full Time'}
                            </div>
                            
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                              </svg>
                              {formatCompensation(job.compensation)}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right space-y-2">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(job.status)}`}>
                          {getStatusText(job.status)}
                        </span>
                        {job.seniority && (
                          <div>
                            <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getSeniorityColor(job.seniority)}`}>
                              {job.seniority.charAt(0).toUpperCase() + job.seniority.slice(1)}
                            </span>
                          </div>
                        )}
                        <p className="text-slate-400 text-xs">
                          Posted {formatDate(job.created_at || job.posted_date || new Date().toISOString())}
                        </p>
                      </div>
                    </div>
                    
                    {/* Job Description */}
                    <div className="mb-4">
                      <p className="text-slate-300 text-sm line-clamp-3">
                        {getJobDescription(job)}
                      </p>
                    </div>

                    {/* Skills */}
                    {job.skills && job.skills.length > 0 && (
                      <div className="mb-4">
                        <p className="text-slate-400 text-xs mb-2 font-medium">Required Skills</p>
                        <div className="flex flex-wrap gap-2">
                          {job.skills.slice(0, 6).map((skill, index) => (
                            <span key={index} className="px-2.5 py-1 bg-blue-500/10 text-blue-300 text-xs rounded-md border border-blue-500/20">
                              {skill}
                            </span>
                          ))}
                          {job.skills.length > 6 && (
                            <span className="px-2.5 py-1 bg-slate-700/50 text-slate-400 text-xs rounded-md">
                              +{job.skills.length - 6} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Legacy Requirements Support */}
                    {job.requirements && job.requirements.length > 0 && !job.skills && (
                      <div className="mb-4">
                        <p className="text-slate-400 text-xs mb-2 font-medium">Requirements</p>
                        <div className="flex flex-wrap gap-2">
                          {job.requirements.slice(0, 4).map((requirement, index) => (
                            <span key={index} className="px-2.5 py-1 bg-slate-700/50 text-slate-300 text-xs rounded-md">
                              {requirement}
                            </span>
                          ))}
                          {job.requirements.length > 4 && (
                            <span className="px-2.5 py-1 bg-slate-700/50 text-slate-400 text-xs rounded-md">
                              +{job.requirements.length - 4} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Company Information */}
                    {job.organization_info && (
                      <div className="pt-4 border-t border-slate-700/50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="text-slate-400 text-xs">
                              <span className="font-medium">Industry:</span> {job.organization_info.industry || 'Not specified'}
                            </div>
                            <div className="text-slate-400 text-xs">
                              <span className="font-medium">Size:</span> {job.organization_info.size || 'Not specified'}
                            </div>
                          </div>
                          {job.organization_info.website && (
                            <a 
                              href={job.organization_info.website} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-400 hover:text-blue-300 text-xs flex items-center transition-colors duration-200"
                            >
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                              </svg>
                              Visit Website
                            </a>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>

      {/* Pagination - Fixed at bottom */}
      {showPagination && jobs.length > 0 && (
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-slate-700/50 flex-shrink-0">
          <button
            onClick={handlePrevious}
            disabled={(filters.skip || 0) === 0}
            className="flex items-center px-4 py-2 text-sm text-slate-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Previous
          </button>
          
          <span className="text-slate-400 text-sm">
            Showing {(filters.skip || 0) + 1}-{(filters.skip || 0) + jobs.length} jobs
          </span>
          
          <button
            onClick={handleNext}
            disabled={jobs.length < limit}
            className="flex items-center px-4 py-2 text-sm text-slate-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            Next
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
} 