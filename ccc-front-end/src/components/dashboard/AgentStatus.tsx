// src/components/dashboard/AgentStatus.tsx
import { useQuery } from '@tanstack/react-query';
import apiClient from '../../lib/apiClient';
import { DashboardSummary } from '../../types';

export default function AgentStatus() {
  const { data, isLoading, error } = useQuery<DashboardSummary, Error>({
    queryKey: ['dashboardSummary'], // Uses the same query key to pull from cache
    queryFn: () => apiClient.get('/dashboard/summary').then(res => res.data),
    refetchInterval: 30000, // Refetch every 30 seconds for live data
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-300 border border-green-500/30';
      case 'idle':
        return 'bg-blue-500/20 text-blue-300 border border-blue-500/30';
      case 'busy':
        return 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30';
      case 'inactive':
        return 'bg-gray-500/20 text-gray-300 border border-gray-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border border-gray-500/30';
    }
  };

  const getStatusDotColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-400';
      case 'idle':
        return 'bg-blue-400';
      case 'busy':
        return 'bg-yellow-400';
      case 'inactive':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'idle':
        return 'Idle';
      case 'busy':
        return 'Busy';
      case 'inactive':
        return 'Inactive';
      default:
        return 'Unknown';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'busy':
        return (
          <svg className="w-4 h-4 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'idle':
        return (
          <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
          </svg>
        );
    }
  };

  return (
    <div className="glass-card rounded-2xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <svg className="w-5 h-5 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Agent Status
      </h3>
      
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-slate-400">Loading agents...</span>
        </div>
      )}
      
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span className="text-red-300 text-sm">Failed to load agent status</span>
          </div>
        </div>
      )}
      
      {data && data.active_agents && data.active_agents.length > 0 ? (
        <div className="space-y-4">
          {data.active_agents.map((agent, index) => (
            <div key={`${agent.name}-${index}`} className="space-y-3">
              {/* Main Agent */}
              <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border border-blue-500/20 rounded-xl">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-xl bg-blue-500/20 flex items-center justify-center">
                    {getStatusIcon(agent.status)}
                  </div>
                  <div className="ml-3">
                    <p className="text-white font-semibold">{agent.name}</p>
                    <p className="text-slate-400 text-xs">Primary Agent</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold ${getStatusColor(agent.status)}`}>
                    <span className={`w-2 h-2 rounded-full mr-2 ${getStatusDotColor(agent.status)}`}></span>
                    {getStatusText(agent.status)}
                  </span>
                </div>
              </div>

              {/* Sub Agents */}
              {agent.sub_agent && agent.sub_agent.length > 0 && (
                <div className="ml-4 space-y-2">
                  <p className="text-slate-400 text-xs font-medium uppercase tracking-wide">Sub Agents</p>
                  {agent.sub_agent.map((subAgent, subIndex) => (
                    <div key={`${subAgent.name}-${subIndex}`} className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg border-l-2 border-purple-500/50">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-lg bg-purple-500/20 flex items-center justify-center">
                          {getStatusIcon(subAgent.status)}
                        </div>
                        <div className="ml-3">
                          <p className="text-white text-sm font-medium">{subAgent.name}</p>
                          <p className="text-slate-400 text-xs">Sub Agent</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getStatusColor(subAgent.status)}`}>
                          <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${getStatusDotColor(subAgent.status)}`}></span>
                          {getStatusText(subAgent.status)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}

          {/* Agent Summary */}
          <div className="bg-slate-800/30 rounded-xl p-4 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <p className="text-slate-400 text-xs uppercase tracking-wide">Total Agents</p>
                <p className="text-white font-semibold text-lg">
                  {data.active_agents.reduce((total, agent) => total + 1 + (agent.sub_agent?.length || 0), 0)}
                </p>
              </div>
              <div className="text-center">
                <p className="text-slate-400 text-xs uppercase tracking-wide">Active Now</p>
                <p className="text-green-400 font-semibold text-lg">
                  {data.active_agents.reduce((total, agent) => {
                    const activeMain = agent.status === 'active' ? 1 : 0;
                    const activeSubs = agent.sub_agent?.filter(sub => sub.status === 'active').length || 0;
                    return total + activeMain + activeSubs;
                  }, 0)}
                </p>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="w-16 h-16 rounded-full bg-slate-700/50 flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
          </div>
          <p className="text-slate-400 text-sm">No active agents available</p>
          <p className="text-slate-500 text-xs mt-1">Agents will appear here when they come online</p>
        </div>
      )}
    </div>
  );
}