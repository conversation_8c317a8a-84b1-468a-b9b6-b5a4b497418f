import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import apiClient from '../../lib/apiClient';
import { Client } from '../../types';

interface MyClientsProps {
  limit?: number;
  showPagination?: boolean;
  className?: string;
}

export default function MyClients({ limit = 20, showPagination = true, className = '' }: MyClientsProps) {
  const [skip, setSkip] = useState(0);

  const { data: clients = [], isLoading, error, refetch } = useQuery<Client[], Error>({
    queryKey: ['myClients', skip, limit],
    queryFn: () => apiClient.get(`/dashboard/my-clients?skip=${skip}&limit=${limit}`).then(res => res.data),
    refetchInterval: 60000, // Refetch every minute
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getJobSearchStatusColor = (status?: string) => {
    switch (status) {
      case 'actively_looking':
        return 'bg-green-500/20 text-green-300 border border-green-500/30';
      case 'passively_looking':
        return 'bg-blue-500/20 text-blue-300 border border-blue-500/30';
      case 'not_looking':
        return 'bg-gray-500/20 text-gray-300 border border-gray-500/30';
      case 'employed':
        return 'bg-purple-500/20 text-purple-300 border border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border border-gray-500/30';
    }
  };

  const getJobSearchStatusText = (status?: string) => {
    switch (status) {
      case 'actively_looking':
        return 'Actively Looking';
      case 'passively_looking':
        return 'Passively Looking';
      case 'not_looking':
        return 'Not Looking';
      case 'employed':
        return 'Employed';
      default:
        return 'Unknown';
    }
  };

  const handlePrevious = () => {
    setSkip(Math.max(0, skip - limit));
  };

  const handleNext = () => {
    if (clients.length === limit) {
      setSkip(skip + limit);
    }
  };

  return (
    <div className={`glass-card rounded-2xl p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white flex items-center">
          <svg className="w-5 h-5 mr-2 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
          My Clients
        </h3>
        <button
          onClick={() => refetch()}
          className="text-slate-400 hover:text-white transition-colors duration-200"
          title="Refresh clients"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>

      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
          <span className="ml-3 text-slate-400">Loading clients...</span>
        </div>
      )}

      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span className="text-red-300 text-sm">Failed to load clients</span>
          </div>
        </div>
      )}

      {!isLoading && !error && (
        <>
          {clients.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 rounded-full bg-slate-700/50 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <p className="text-slate-400 text-sm">No clients found</p>
              <p className="text-slate-500 text-xs mt-1">Your clients will appear here once they&apos;re assigned</p>
            </div>
          ) : (
            <div className="space-y-4">
              {clients.map((client) => (
                <div key={client.client_id} className="bg-slate-800/30 rounded-xl p-4 border border-slate-700/50 hover:border-slate-600/50 transition-colors duration-200">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-indigo-500/20 to-purple-500/20 flex items-center justify-center">
                        <span className="text-white font-semibold text-lg">
                          {client.client_full_name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                        </span>
                      </div>
                      <div className="flex-1">
                        <h4 className="text-white font-semibold">{client.client_full_name}</h4>
                        {client.profile ? (
                          <div className="space-y-2 mt-2">
                            {client.profile.email && (
                              <p className="text-slate-400 text-sm">{client.profile.email}</p>
                            )}
                            {client.profile.location && (
                              <p className="text-slate-400 text-sm flex items-center">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                {client.profile.location}
                              </p>
                            )}
                            {client.profile.experience_level && (
                              <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30">
                                {client.profile.experience_level}
                              </span>
                            )}
                          </div>
                        ) : (
                          <p className="text-slate-500 text-sm mt-1">Profile not available</p>
                        )}
                      </div>
                    </div>
                    <div className="text-right space-y-2">
                      {client.profile?.job_search_status && (
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getJobSearchStatusColor(client.profile.job_search_status)}`}>
                          {getJobSearchStatusText(client.profile.job_search_status)}
                        </span>
                      )}
                      <p className="text-slate-400 text-xs">
                        Added {formatDate(client.added_at)}
                      </p>
                    </div>
                  </div>
                  
                  {client.profile?.skills && client.profile.skills.length > 0 && (
                    <div className="mt-4">
                      <p className="text-slate-400 text-xs mb-2">Skills</p>
                      <div className="flex flex-wrap gap-2">
                        {client.profile.skills.slice(0, 5).map((skill, index) => (
                          <span key={index} className="px-2 py-1 bg-slate-700/50 text-slate-300 text-xs rounded-md">
                            {skill}
                          </span>
                        ))}
                        {client.profile.skills.length > 5 && (
                          <span className="px-2 py-1 bg-slate-700/50 text-slate-400 text-xs rounded-md">
                            +{client.profile.skills.length - 5} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {showPagination && clients.length > 0 && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t border-slate-700/50">
              <button
                onClick={handlePrevious}
                disabled={skip === 0}
                className="flex items-center px-4 py-2 text-sm text-slate-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous
              </button>
              
              <span className="text-slate-400 text-sm">
                Showing {skip + 1}-{skip + clients.length} clients
              </span>
              
              <button
                onClick={handleNext}
                disabled={clients.length < limit}
                className="flex items-center px-4 py-2 text-sm text-slate-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                Next
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
} 