import { useAuthStore } from '../store/useAuthStore';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import Link from 'next/link';

export default function LandingPage() {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const router = useRouter();

  useEffect(() => {
    // If user is already authenticated, redirect to dashboard
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  return (
    <div className="min-h-screen gradient-bg">
      <div className="container mx-auto px-6 py-12">
        {/* Header */}
        <header className="text-center mb-20 animate-fade-in">
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-20 h-20 rounded-2xl gradient-accent mb-6">
              <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h1 className="text-7xl font-bold text-white mb-6 tracking-tight">
              Career Coach <span className="bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">Central</span>
            </h1>
          </div>
          <p className="text-xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
            Transform your career coaching practice with AI-powered conversations. 
            Streamline client interactions, generate insights, and deliver personalized 
            career development reports through intelligent dialogue.
          </p>
        </header>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20 animate-slide-up">
          <div className="glass-card rounded-2xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">24</div>
            <div className="text-sm text-slate-400">Active Talents</div>
            <div className="text-xs text-green-400 mt-1">+2 this week</div>
          </div>
          <div className="glass-card rounded-2xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">89%</div>
            <div className="text-sm text-slate-400">Success Rate</div>
            <div className="text-xs text-green-400 mt-1">+3% vs last month</div>
          </div>
          <div className="glass-card rounded-2xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">156</div>
            <div className="text-sm text-slate-400">Reports Generated</div>
            <div className="text-xs text-green-400 mt-1">+12% vs avg</div>
          </div>
          <div className="glass-card rounded-2xl p-6 text-center">
            <div className="text-3xl font-bold text-white mb-2">4.8</div>
            <div className="text-sm text-slate-400">Avg. Rating</div>
            <div className="text-xs text-slate-400 mt-1">steady</div>
          </div>
        </div>

        {/* Features Section */}
        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          <div className="glass-card rounded-2xl p-8 animate-slide-up" style={{animationDelay: '0.1s'}}>
            <div className="w-14 h-14 rounded-xl gradient-accent flex items-center justify-center mb-6">
              <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-white mb-4">AI-Powered Conversations</h3>
            <p className="text-slate-400 leading-relaxed">
              Engage with intelligent agents that understand your coaching needs and provide 
              real-time insights through natural conversation.
            </p>
            <div className="mt-6">
              <span className="inline-flex items-center text-sm text-indigo-400">
                <span className="status-dot status-active"></span>
                Active now
              </span>
            </div>
          </div>

          <div className="glass-card rounded-2xl p-8 animate-slide-up" style={{animationDelay: '0.2s'}}>
            <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center mb-6">
              <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-white mb-4">Real-time Analytics</h3>
            <p className="text-slate-400 leading-relaxed">
              Monitor your coaching practice with live dashboards showing client progress, 
              engagement metrics, and actionable insights.
            </p>
            <div className="mt-6">
              <span className="inline-flex items-center text-sm text-green-400">
                <span className="status-dot status-active"></span>
                Live data
              </span>
            </div>
          </div>

          <div className="glass-card rounded-2xl p-8 animate-slide-up" style={{animationDelay: '0.3s'}}>
            <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center mb-6">
              <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-white mb-4">Automated Reports</h3>
            <p className="text-slate-400 leading-relaxed">
              Generate comprehensive career development reports automatically, 
              saving hours of manual work while delivering professional results.
            </p>
            <div className="mt-6">
              <span className="inline-flex items-center text-sm text-purple-400">
                <span className="status-dot status-active"></span>
                Auto-generated
              </span>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center animate-fade-in">
          <div className="glass-card rounded-3xl p-12 max-w-3xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Transform Your Practice?
            </h2>
            <p className="text-slate-300 mb-10 text-lg leading-relaxed">
              Join career coaches who are already using AI to enhance their client relationships 
              and streamline their workflow. Start your journey today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link 
                href="/login"
                className="inline-flex items-center px-8 py-4 rounded-xl gradient-accent text-white font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Get Started Now
              </Link>
              <button className="inline-flex items-center px-8 py-4 rounded-xl border border-slate-600 text-slate-300 font-semibold text-lg transition-all duration-200 hover:bg-slate-800/50">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Watch Demo
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="text-center mt-20 pt-8 border-t border-slate-700/50">
          <p className="text-slate-400">
            © 2024 Career Coach Central. Empowering careers through intelligent conversation.
          </p>
        </footer>
      </div>
    </div>
  );
} 