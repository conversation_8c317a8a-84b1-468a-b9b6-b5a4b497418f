import type { AppProps } from 'next/app';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { useState, useEffect } from 'react';
import { useAuthStore } from '../store/useAuthStore';
import { useTokenRefresh } from '../hooks/useTokenRefresh';
import { TokenDebugger } from '../components/common/TokenDebugger';
import '../app/globals.css';

function AppContent({ Component, pageProps }: AppProps) {
  const initializeAuthListener = useAuthStore((state) => state.initializeAuthListener);
  
  // Initialize token refresh functionality
  useTokenRefresh();

  useEffect(() => {
    // Initialize Firebase auth state listener
    const unsubscribe = initializeAuthListener();
    
    // Cleanup function to unsubscribe when component unmounts
    return () => {
      unsubscribe();
    };
  }, [initializeAuthListener]);

  return (
    <>
      <Component {...pageProps} />
      <TokenDebugger />
    </>
  );
}

export default function App(props: AppProps) {
  const [queryClient] = useState(() => new QueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      <AppContent {...props} />
      <Toaster position="top-right" />
    </QueryClientProvider>
  );
} 