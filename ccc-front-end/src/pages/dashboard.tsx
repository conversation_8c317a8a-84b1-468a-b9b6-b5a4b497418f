// src/pages/dashboard.tsx
import { useState, useEffect, useRef, useMemo } from 'react';
import { useChatStore } from '../store/useChatStore';
import { useAuthStore } from '../store/useAuthStore';
import ProtectedRoute from '../components/layout/ProtectedRoute';
import { useRouter } from 'next/router';
import toast from 'react-hot-toast';

import DashboardSummary from '../components/dashboard/DashboardSummary';
import MessageBubble from '@/components/chat/MessageBubble';
import ChatInput from '@/components/chat/ChatInput';
import ChatSessionSidebar from '@/components/chat/ChatSessionSidebar';
import AgentStatus from '@/components/dashboard/AgentStatus';
import MyClients from '@/components/dashboard/MyClients';
import TalentPool from '@/components/dashboard/TalentPool';
import Jobs from '@/components/dashboard/Jobs';

type DashboardTab = 'overview' | 'clients' | 'talent-pool' | 'jobs' | 'chat';

export default function DashboardPage() {
  // Note: useChatStream is initialized when sending messages, no need to call it here
  const [activeTab, setActiveTab] = useState<DashboardTab>('overview');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  
  // Optimize chat store selectors to prevent excessive re-renders
  const streamingStatus = useChatStore((state) => state.streamingStatus);
  const connectionStatus = useChatStore((state) => state.connectionStatus);
  const currentSessionId = useChatStore((state) => state.currentSessionId);
  const sessions = useChatStore((state) => state.sessions);
  
  const { logout, user } = useAuthStore();
  const router = useRouter();
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Memoize current session and messages to prevent unnecessary re-computations
  const currentSession = useMemo(() => {
    return currentSessionId ? sessions[currentSessionId] : null;
  }, [currentSessionId, sessions]);
  
  const messages = useMemo(() => {
    return currentSession?.messages || [];
  }, [currentSession?.messages]);

  // Auto-scroll to bottom when new messages arrive or when content updates
  useEffect(() => {
    if (chatContainerRef.current && activeTab === 'chat') {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages, activeTab]);

  const handleLogout = () => {
    logout();
    toast.success('Logged out successfully');
    router.push('/');
  };

  const tabs = [
    { id: 'overview' as DashboardTab, label: 'Overview', icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    )},
    { id: 'clients' as DashboardTab, label: 'My Clients', icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>
    )},
    { id: 'talent-pool' as DashboardTab, label: 'Talent Pool', icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    )},
    { id: 'jobs' as DashboardTab, label: 'Jobs', icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8m0 0H4a2 2 0 00-2 2v6a2 2 0 002 2h2m2-6h8m-8 0v6a2 2 0 002 2h4a2 2 0 002-2v-6m-8 0V8a2 2 0 012-2h4a2 2 0 012 2v2" />
      </svg>
    )},
    { id: 'chat' as DashboardTab, label: 'AI Assistant', icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
      </svg>
    )}
  ];
  
  return (
    <ProtectedRoute>
      <div className="h-screen gradient-bg flex flex-col">
        {/* Header */}
        <header className="glass-card border-b border-slate-700/50 px-6 py-4 flex justify-between items-center animate-fade-in">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 rounded-xl gradient-accent flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">Career Coach Central</h1>
              <p className="text-slate-400 text-sm">
                {user?.full_name ? `Welcome back, ${user.full_name}` : 'AI-Powered Career Coaching Dashboard'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {user && (
              <div className="text-right">
                <p className="text-white text-sm font-medium">{user.full_name || 'Coach'}</p>
                <p className="text-slate-400 text-xs">{user.email}</p>
              </div>
            )}
            <button
              onClick={handleLogout}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center group"
            >
              <svg className="w-4 h-4 mr-2 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Logout
            </button>
          </div>
        </header>

        {/* Navigation Tabs */}
        <nav className="glass-card border-b border-slate-700/50 px-6 animate-fade-in">
          <div className="flex space-x-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-3 rounded-t-lg transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-slate-800/50 text-white border-b-2 border-indigo-500'
                    : 'text-slate-400 hover:text-white hover:bg-slate-800/30'
                }`}
              >
                {tab.icon}
                <span className="font-medium">{tab.label}</span>
                {tab.id === 'chat' && messages.length > 0 && (
                  <span className="bg-indigo-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] h-5 flex items-center justify-center">
                    {messages.length}
                  </span>
                )}
              </button>
            ))}
          </div>
        </nav>

        {/* Main Content Area */}
        <div className="flex-1 p-6 overflow-hidden">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 h-full animate-slide-up">
              <div className="xl:col-span-2 space-y-6">
                <DashboardSummary />
                <MyClients limit={5} showPagination={false} />
              </div>
              <div className="space-y-6">
                <AgentStatus />
                {/* Quick Actions */}
                <div className="glass-card rounded-2xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    Quick Actions
                  </h3>
                  <div className="space-y-3">
                    <button
                      onClick={() => setActiveTab('clients')}
                      className="w-full flex items-center justify-between p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors duration-200"
                    >
                      <span className="text-white font-medium">View All Clients</span>
                      <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                    <button
                      onClick={() => setActiveTab('talent-pool')}
                      className="w-full flex items-center justify-between p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors duration-200"
                    >
                      <span className="text-white font-medium">Browse Talent Pool</span>
                      <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                    <button
                      onClick={() => setActiveTab('jobs')}
                      className="w-full flex items-center justify-between p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors duration-200"
                    >
                      <span className="text-white font-medium">View Job Listings</span>
                      <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                    <button
                      onClick={() => setActiveTab('chat')}
                      className="w-full flex items-center justify-between p-3 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 border border-indigo-500/30 rounded-xl hover:from-indigo-500/30 hover:to-purple-500/30 transition-all duration-200"
                    >
                      <span className="text-white font-medium">AI Assistant</span>
                      <svg className="w-4 h-4 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'clients' && (
            <div className="h-full animate-slide-up">
              <MyClients className="h-full" />
            </div>
          )}

          {activeTab === 'talent-pool' && (
            <div className="h-full animate-slide-up">
              <TalentPool className="h-full" />
            </div>
          )}

          {activeTab === 'jobs' && (
            <div className="h-full animate-slide-up">
              <Jobs className="h-full" />
            </div>
          )}

          {activeTab === 'chat' && (
            <div className="h-full flex flex-col glass-card rounded-2xl overflow-hidden animate-slide-up relative">
              {/* Chat Session Sidebar */}
              <ChatSessionSidebar 
                isOpen={isSidebarOpen} 
                onClose={() => setIsSidebarOpen(false)} 
              />
              
              {/* Chat Header with Status */}
              <div className="flex items-center justify-between px-6 py-4 border-b border-slate-700/50">
                <div className="flex items-center space-x-3">
                  {/* Session Sidebar Toggle */}
                  <button
                    onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                    className="p-2 hover:bg-slate-700/50 rounded-lg transition-colors"
                  >
                    <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                  </button>
                  
                  <h2 className="text-lg font-semibold text-white flex items-center">
                    <svg className="w-5 h-5 mr-2 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    {currentSession?.title || 'AI Assistant'}
                  </h2>
                </div>
                
                <div className="flex items-center space-x-3">
                  {/* Streaming Status */}
                  {streamingStatus === 'streaming' && (
                    <div className="flex items-center space-x-2 px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-full">
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                      <span className="text-blue-300 text-sm font-medium">AI is thinking...</span>
                    </div>
                  )}
                  {/* Connection Status */}
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                    connectionStatus === 'connected' 
                      ? 'bg-green-500/20 text-green-300'
                      : connectionStatus === 'connecting'
                      ? 'bg-yellow-500/20 text-yellow-300'
                      : 'bg-red-500/20 text-red-300'
                  }`}>
                    <div className={`w-1.5 h-1.5 rounded-full ${
                      connectionStatus === 'connected' 
                        ? 'bg-green-400'
                        : connectionStatus === 'connecting'
                        ? 'bg-yellow-400 animate-pulse'
                        : 'bg-red-400'
                    }`}></div>
                    <span className="capitalize">{connectionStatus}</span>
                  </div>
                </div>
              </div>
              
              {/* Chat Content */}
              <div ref={chatContainerRef} className="flex-1 overflow-y-auto p-6">
                {messages.length === 0 ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center max-w-md">
                      <div className="w-20 h-20 rounded-2xl gradient-accent flex items-center justify-center mx-auto mb-6">
                        <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                      </div>
                      <h3 className="text-2xl font-semibold text-white mb-4">Welcome to Your AI Assistant</h3>
                      <p className="text-slate-400 leading-relaxed">
                        Start a conversation below to get insights about your coaching practice, 
                        generate reports, or ask questions about your clients.
                      </p>
                      <div className="mt-6 flex flex-wrap justify-center gap-2">
                        <span className="px-3 py-1 bg-indigo-500/20 text-indigo-300 rounded-full text-sm">Career Insights</span>
                        <span className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm">Report Generation</span>
                        <span className="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm">Client Analysis</span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.map((msg) => (
                      <MessageBubble key={msg.id} message={msg} />
                    ))}
                  </div>
                )}
              </div>
              
              {/* Chat Input */}
              <div className="border-t border-slate-700/50 p-4">
                <ChatInput />
              </div>
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
}