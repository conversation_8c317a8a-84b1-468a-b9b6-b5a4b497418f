// src/pages/reports/[reportId].tsx
import { GetServerSideProps } from 'next';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import 'highlight.js/styles/github-dark.css';
import apiClient from '../../lib/apiClient';
import { SendReportButton } from '../../components/report/SendReportButton';
import Link from 'next/link';

interface ReportPageProps {
  markdownContent: string;
  reportToken: string;
  error?: string;
}



interface ApiError {
  response?: {
    status?: number;
    statusText?: string;
    data?: unknown;
  };
}

export default function ReportPage({ markdownContent, reportToken, error }: ReportPageProps) {
  if (error) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center p-4">
        <div className="glass-card rounded-2xl p-8 max-w-md w-full text-center animate-fade-in">
          <div className="w-16 h-16 rounded-full bg-red-500/20 flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-white mb-2">Report Not Found</h2>
          <p className="text-slate-400 mb-6">{error}</p>
          <Link 
            href="/dashboard"
            className="inline-flex items-center px-6 py-3 rounded-xl gradient-accent text-white font-semibold transition-all duration-200 hover:scale-105"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen gradient-bg">
      {/* Header */}
      <header className="glass-card border-b border-slate-700/50 px-6 py-4 animate-fade-in">
        <div className="max-w-6xl mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 rounded-xl gradient-accent flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">Career Development Report</h1>
              <p className="text-slate-400 text-sm">Professional coaching insights and recommendations</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Link 
              href="/dashboard"
              className="inline-flex items-center px-4 py-2 rounded-xl border border-slate-600 text-slate-300 font-semibold transition-all duration-200 hover:bg-slate-800/50"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Dashboard
            </Link>
            <SendReportButton reportId={reportToken} />
          </div>
        </div>
      </header>

      {/* Report Content */}
      <main className="max-w-6xl mx-auto p-6 animate-slide-up">
        <div className="glass-card rounded-2xl overflow-hidden">
          {/* Report Header */}
          <div className="p-8 border-b border-slate-700/50">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold text-white mb-2">Career Assessment Report</h2>
                <p className="text-slate-400">Generated on {new Date().toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}</p>
              </div>
              <div className="text-right">
                <div className="text-sm text-slate-400 mb-1">Report Token</div>
                <div className="text-white font-mono text-sm bg-slate-800/50 px-3 py-1 rounded-lg">
                  {reportToken}
                </div>
              </div>
            </div>
          </div>

          {/* Report Body */}
          <div className="p-8">
            <div className="prose prose-invert prose-lg max-w-none 
                         prose-headings:text-white prose-headings:font-semibold prose-headings:mb-4 prose-headings:mt-8
                         prose-h1:text-4xl prose-h1:mb-6 prose-h1:mt-0 prose-h1:border-b prose-h1:border-slate-700 prose-h1:pb-4
                         prose-h2:text-3xl prose-h2:mb-4 prose-h2:mt-8 prose-h2:text-indigo-300
                         prose-h3:text-2xl prose-h3:mb-3 prose-h3:mt-6 prose-h3:text-indigo-400
                         prose-h4:text-xl prose-h4:mb-2 prose-h4:mt-5 prose-h4:text-indigo-500
                         prose-p:text-slate-300 prose-p:leading-relaxed prose-p:mb-4
                         prose-strong:text-white prose-strong:font-semibold
                         prose-em:text-indigo-300 prose-em:italic
                         prose-ul:text-slate-300 prose-ul:space-y-2 prose-ul:mb-4
                         prose-ol:text-slate-300 prose-ol:space-y-2 prose-ol:mb-4
                         prose-li:text-slate-300 prose-li:leading-relaxed prose-li:mb-1
                         prose-a:text-indigo-400 prose-a:no-underline prose-a:font-medium hover:prose-a:text-indigo-300 hover:prose-a:underline
                         prose-blockquote:border-l-4 prose-blockquote:border-indigo-500 prose-blockquote:bg-slate-800/30 
                         prose-blockquote:rounded-r-lg prose-blockquote:p-4 prose-blockquote:my-6 prose-blockquote:text-slate-300
                         prose-code:text-indigo-300 prose-code:bg-slate-800/50 prose-code:px-2 prose-code:py-1 
                         prose-code:rounded prose-code:text-sm prose-code:font-mono
                         prose-pre:bg-slate-900/80 prose-pre:border prose-pre:border-slate-700 prose-pre:rounded-lg
                         prose-pre:p-4 prose-pre:overflow-x-auto prose-pre:my-6
                         prose-table:w-full prose-table:border-collapse prose-table:border prose-table:border-slate-700
                         prose-thead:bg-slate-800/50 prose-th:border prose-th:border-slate-700 prose-th:p-3 prose-th:text-left prose-th:font-semibold prose-th:text-white
                         prose-td:border prose-td:border-slate-700 prose-td:p-3 prose-td:text-slate-300
                         prose-hr:border-slate-700 prose-hr:my-8">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeHighlight]}
                components={{
                  // Custom components for better styling
                  h1: ({ children }) => (
                    <h1 className="text-4xl font-bold text-white mb-6 mt-0 border-b border-slate-700 pb-4">
                      {children}
                    </h1>
                  ),
                  h2: ({ children }) => (
                    <h2 className="text-3xl font-semibold text-indigo-300 mb-4 mt-8">
                      {children}
                    </h2>
                  ),
                  h3: ({ children }) => (
                    <h3 className="text-2xl font-semibold text-indigo-400 mb-3 mt-6">
                      {children}
                    </h3>
                  ),
                  h4: ({ children }) => (
                    <h4 className="text-xl font-semibold text-indigo-500 mb-2 mt-5">
                      {children}
                    </h4>
                  ),
                  p: ({ children }) => (
                    <p className="text-slate-300 leading-relaxed mb-4">
                      {children}
                    </p>
                  ),
                  ul: ({ children }) => (
                    <ul className="text-slate-300 space-y-2 mb-4 list-disc list-inside">
                      {children}
                    </ul>
                  ),
                  ol: ({ children }) => (
                    <ol className="text-slate-300 space-y-2 mb-4 list-decimal list-inside">
                      {children}
                    </ol>
                  ),
                  li: ({ children }) => (
                    <li className="text-slate-300 leading-relaxed mb-1">
                      {children}
                    </li>
                  ),
                  blockquote: ({ children }) => (
                    <blockquote className="border-l-4 border-indigo-500 bg-slate-800/30 rounded-r-lg p-4 my-6 text-slate-300 italic">
                      {children}
                    </blockquote>
                  ),
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  code: ({ inline, children, ...props }: any) => {
                    if (inline) {
                      return (
                        <code className="text-indigo-300 bg-slate-800/50 px-2 py-1 rounded text-sm font-mono" {...props}>
                          {children}
                        </code>
                      );
                    }
                    return (
                      <code {...props}>
                        {children}
                      </code>
                    );
                  },
                  pre: ({ children }) => (
                    <pre className="bg-slate-900/80 border border-slate-700 rounded-lg p-4 overflow-x-auto my-6">
                      {children}
                    </pre>
                  ),
                  table: ({ children }) => (
                    <div className="overflow-x-auto my-6">
                      <table className="w-full border-collapse border border-slate-700 rounded-lg">
                        {children}
                      </table>
                    </div>
                  ),
                  thead: ({ children }) => (
                    <thead className="bg-slate-800/50">
                      {children}
                    </thead>
                  ),
                  th: ({ children }) => (
                    <th className="border border-slate-700 p-3 text-left font-semibold text-white">
                      {children}
                    </th>
                  ),
                  td: ({ children }) => (
                    <td className="border border-slate-700 p-3 text-slate-300">
                      {children}
                    </td>
                  ),
                  hr: () => (
                    <hr className="border-slate-700 my-8" />
                  ),
                  a: ({ href, children }) => (
                    <a 
                      href={href} 
                      className="text-indigo-400 font-medium hover:text-indigo-300 hover:underline transition-colors"
                      target="_blank" 
                      rel="noopener noreferrer"
                    >
                      {children}
                    </a>
                  ),
                }}
              >
                {markdownContent}
              </ReactMarkdown>
            </div>
          </div>

          {/* Report Footer */}
          <div className="p-8 border-t border-slate-700/50 bg-slate-800/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 rounded-lg gradient-accent flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <p className="text-white font-semibold">Career Coach Central</p>
                  <p className="text-slate-400 text-sm">AI-Powered Career Coaching Platform</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-slate-400 text-sm">
                  This report is confidential and intended for the recipient only.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { reportId } = context.query;

  if (!reportId) {
    return { notFound: true };
  }

  try {
    // Use the token (reportId) as the route parameter
    const response = await apiClient.get(`/reports/${reportId}`);

    console.log('Report response:', response.data);
    return {
      props: { 
        markdownContent: response.data, 
        reportToken: reportId as string 
      },
    };
  } catch (error) {
    // Log the error details for debugging
    const apiError = error as Error & ApiError;
    console.error('Failed to fetch report:', {
      reportToken: reportId,
      error: apiError.message || 'Unknown error',
      status: apiError.response?.status,
      statusText: apiError.response?.statusText,
      data: apiError.response?.data,
      timestamp: new Date().toISOString(),
    });

    return {
      props: {
        markdownContent: '',
        reportToken: reportId as string,
        error: 'Failed to load report. It may be invalid or expired.',
      },
    };
  }
};