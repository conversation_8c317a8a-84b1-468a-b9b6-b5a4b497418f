// src/types/index.ts

interface FunctionCall {
  name: string;
  [key: string]: unknown;
}

interface FunctionResponse {
  name: string;
  [key: string]: unknown;
}

interface MessageActions {
  state_delta?: unknown;
  [key: string]: unknown;
}

export interface ChatMessage {
  id: string;
  type: 'user_message' | 'agent_message' | 'status' | 'report_link' | 'error';
  content: string;
  timestamp: string;
  author: string;
  partial?: boolean;
  url?: string; // For report_link type
  functionCalls?: FunctionCall[]; // For function_calls event type
  functionResponses?: FunctionResponse[]; // For function_responses event type
  actions?: MessageActions; // For actions event type
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
}

export interface Coach {
  id: string;
  email: string;
  full_name: string | null;
  created_at: string;
  last_login_at: string | null;
  is_active: boolean;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: Coach;
}

export interface SubAgent {
  name: string;
  status: 'active' | 'inactive' | 'idle' | 'busy';
}

export interface Agent {
  name: string;
  status: 'active' | 'inactive' | 'idle' | 'busy';
  sub_agent?: SubAgent[];
}

// Dashboard API Types
export interface DashboardSummary {
  my_clients_count: number;
  global_talent_pool_count: number;
  active_jobs_count: number;
  active_agents: Agent[];
}

export interface ClientProfile {
  id?: string;
  full_name?: string;
  email?: string;
  skills?: string[];
  experience_level?: string;
  job_search_status?: string;
  preferred_industries?: string[];
  location?: string;
  [key: string]: unknown; // For additional profile fields
}

export interface Client {
  client_id: string;
  cosmos_user_id: string;
  cosmos_profile_id: string;
  client_full_name: string;
  added_at: string;
  profile: ClientProfile | null;
}

// Updated TalentProfile to match actual API structure
export interface TalentProfile {
  id: string;
  userId: string;
  externalId: string;
  profileVersion: number;
  lastUpdatedAt: string;
  profileSchemaVersion: string;
  basicInfo?: {
    fullName?: string;
    preferredName?: string;
    gender?: string;
    dateOfBirth?: string;
    primaryLanguage?: string;
  };
  contactInfo?: {
    emails?: Array<{
      email: string;
      isPrimary: boolean;
      verified: boolean;
    }>;
    phones?: Array<{
      phoneNumber: string;
      phoneType: string;
      isPrimary: boolean;
    }>;
    addresses?: Array<{
      addressLine1: string;
      city: string;
      stateProvince: string;
      postalCode: string;
      country: string;
    }>;
    socialMediaLinks?: Array<{
      platformName: string;
      profileUrl: string;
    }>;
  };
  professionalSummary?: {
    candidateHeadline?: string;
  };
  education?: Array<{
    institutionName: string;
    startDate: string;
    endDate?: string;
    isPresent: boolean;
  }>;
  workExperience?: Array<{
    companyName: string;
    jobTitle: string;
    startDate: string;
    endDate?: string;
    isPresent: boolean;
    responsibilities?: string[];
    skillsUsed?: string[];
  }>;
  skillsAndExpertise?: Array<{
    skillName: string;
    proficiency: string;
    yearsExperience: number;
  }>;
  preferences?: {
    jobSearchStatus?: string;
    preferredIndustries?: string[];
  };
  // Legacy fields for backward compatibility
  full_name?: string;
  email?: string;
  job_search_status?: string;
  preferred_industries?: string[];
  skills?: string[];
  experience_level?: string;
  location?: string;
  [key: string]: unknown; // For additional profile fields
}

export interface Compensation {
  min_cents: number;
  max_cents: number;
  currency: string;
  period: 'YEARLY' | 'MONTHLY' | 'HOURLY';
  offers_equity: boolean;
}

export interface OrganizationInfo {
  name: string;
  description: string;
  logo_url?: string;
  stage?: string;
  head_count?: string;
  topics: string[];
  industry_tags: string[];
  slug?: string;
  website?: string;
  location?: string;
  email?: string;
  phone?: string;
  industry?: string;
  size?: string;
  id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Job {
  id: string;
  title: string;
  job_description: string;
  company_id: string;
  organization_info: OrganizationInfo;
  slug: string;
  status: 'active' | 'closed' | 'draft' | 'expired';
  job_type: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERNSHIP';
  is_valid: boolean;
  has_description: boolean;
  is_community_create: boolean;
  locations: string[];
  work_mode: 'remote' | 'on_site' | 'hybrid';
  seniority: 'entry' | 'junior' | 'mid' | 'senior' | 'lead' | 'executive';
  skills: string[];
  summary: string;
  qualifications: string;
  additional_info?: string;
  url?: string;
  expires_at?: string;
  compensation: Compensation;
  creator_id: string;
  created_at: string;
  updated_at: string;
  // Legacy fields for backward compatibility
  company?: string;
  location?: string;
  posted_date?: string;
  description?: string;
  requirements?: string[];
  salary_range?: string;
  [key: string]: unknown; // For additional job fields
}

// Pagination and filtering types
export interface PaginationParams {
  skip?: number;
  limit?: number;
}

export interface TalentPoolFilters extends PaginationParams {
  job_search_status?: string;
  preferred_industries?: string[];
}

export interface JobFilters extends PaginationParams {
  status?: string;
  company_id?: string;
}

export interface AddClientRequest {
  cosmos_profile_id: string;
}

export interface AddClientResponse {
  success: boolean;
  message: string;
  client: {
    client_id: string;
    cosmos_user_id: string;
    cosmos_profile_id: string;
    client_full_name: string;
    added_at: string;
  };
}