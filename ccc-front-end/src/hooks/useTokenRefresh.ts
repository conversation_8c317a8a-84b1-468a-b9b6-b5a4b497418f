import { useEffect, useCallback } from 'react';
import { useAuthStore } from '../store/useAuthStore';
import { shouldRefreshToken, logTokenInfo } from '../lib/tokenUtils';

export const useTokenRefresh = () => {
  const { sessionToken, firebaseUser, refreshToken, isAuthenticated } = useAuthStore();

  // Function to manually refresh token
  const manualRefresh = useCallback(async () => {
    if (firebaseUser && isAuthenticated) {
      try {
        await refreshToken();
        return true;
      } catch (error) {
        console.error('Manual token refresh failed:', error);
        return false;
      }
    }
    return false;
  }, [firebaseUser, isAuthenticated, refreshToken]);

  // Check token validity on mount and when token changes
  useEffect(() => {
    const checkTokenValidity = async () => {
      if (sessionToken && firebaseUser && isAuthenticated) {
        try {
          // Log current token info for debugging
          if (process.env.NODE_ENV === 'development') {
            logTokenInfo(sessionToken, 'Current Token');
          }
          
          // Check if token needs refresh
          if (shouldRefreshToken(sessionToken)) {
            console.log('To<PERSON> needs refresh, refreshing...');
            await refreshToken();
          }
        } catch (error) {
          console.error('Error checking token validity:', error);
          // If we can't analyze the token, try to refresh it
          await refreshToken();
        }
      }
    };

    checkTokenValidity();
  }, [sessionToken, firebaseUser, isAuthenticated, refreshToken]);

  return {
    manualRefresh,
    isTokenValid: !!sessionToken && isAuthenticated,
  };
}; 