// src/hooks/useChatStream.ts
import { useCallback } from 'react';
import { useAuthStore } from '../store/useAuthStore';
import { useChatStore } from '../store/useChatStore';
import { ChatMessage } from '../types';

interface StreamEventData {
  id: string;
  event_type: string;
  timestamp: number;
  author: string;
  content?: {
    data: string;
    partial?: boolean;
  };
  function_calls?: Array<{
    name: string;
    [key: string]: unknown;
  }>;
  function_responses?: Array<{
    name: string;
    [key: string]: unknown;
  }>;
  actions?: {
    state_delta?: unknown;
    [key: string]: unknown;
  };
}

export function useChatStream() {
  const sessionToken = useAuthStore((state) => state.sessionToken);
  const { 
    addMessage, 
    setConnectionStatus, 
    updateLastMessage, 
    setStreamingStatus,
    getCurrentSession,
    createSession,
    currentSessionId
  } = useChatStore();

  const sendMessage = useCallback(async (content: string, clientIds: string[] = []) => {
    if (!sessionToken) return;

    // Ensure we have a current session - create one if none exists
    let sessionId = currentSessionId;
    if (!sessionId || !getCurrentSession()) {
      sessionId = createSession();
    }

    // Format function name for display
    const formatFunctionName = (functionName: string): string => {
      return functionName
        .replace(/_/g, ' ')
        .replace(/\b\w/g, (char) => char.toUpperCase())
        .trim();
    };

    const handleStreamEvent = async (eventData: StreamEventData, currentAgentMessage: ChatMessage | null) => {
      const { event_type, timestamp, author } = eventData;
      
      switch (event_type) {
        case 'function_calls':
          // Show function call status
          const functionCallMessage: ChatMessage = {
            id: `${eventData.id}_call`,
            type: 'status',
            content: `🔧 Calling function: ${formatFunctionName(eventData.function_calls?.[0]?.name || 'Unknown')}`,
            timestamp: new Date(timestamp * 1000).toISOString(),
            author,
            functionCalls: eventData.function_calls
          };
          addMessage(functionCallMessage);
          break;

        case 'function_responses':
          // Show function response status
          const functionResponseMessage: ChatMessage = {
            id: `${eventData.id}_response`,
            type: 'status',
            content: `✅ Function completed: ${formatFunctionName(eventData.function_responses?.[0]?.name || 'Unknown')}`,
            timestamp: new Date(timestamp * 1000).toISOString(),
            author,
            functionResponses: eventData.function_responses
          };
          addMessage(functionResponseMessage);
          break;

        case 'text_content':
          // This is handled in the main loop to update the current message
          break;

        case 'final_text':
          // Mark the message as final
          if (currentAgentMessage) {
            currentAgentMessage.partial = false;
            currentAgentMessage.content = eventData.content?.data || '';
            updateLastMessage(currentAgentMessage);
          }
          break;

        case 'actions':
          // Show state delta actions
          if (eventData.actions?.state_delta) {
            const actionMessage: ChatMessage = {
              id: `${eventData.id}_action`,
              type: 'status',
              content: '📊 Updating state with new insights',
              timestamp: new Date(timestamp * 1000).toISOString(),
              author,
              actions: eventData.actions
            };
            addMessage(actionMessage);
          }
          break;

        case 'final_response':
          // Stream has ended
          setStreamingStatus('idle');
          break;

        default:
          console.log('Unknown event type:', event_type, eventData);
      }
    };

    // Add user message immediately
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user_message',
      content,
      timestamp: new Date().toISOString(),
      author: 'user'
    };
    addMessage(userMessage);

    try {
      setConnectionStatus('connecting');
      setStreamingStatus('streaming');

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionToken}`,
        },
        body: JSON.stringify({
          message: content,
          client_ids: clientIds,
          session_id: sessionId
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      setConnectionStatus('connected');

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No reader available');
      }

      let currentAgentMessage: ChatMessage | null = null;
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const eventData = JSON.parse(line.slice(6)) as StreamEventData;
              
              // Handle text content updates immediately to ensure real-time display
              if (eventData.event_type === 'text_content' && eventData.content?.data) {
                if (!currentAgentMessage) {
                  // Create new message for streaming text
                  currentAgentMessage = {
                    id: eventData.id,
                    type: 'agent_message',
                    content: eventData.content.data,
                    timestamp: new Date(eventData.timestamp * 1000).toISOString(),
                    author: eventData.author,
                    partial: eventData.content.partial ?? true
                  };
                  addMessage(currentAgentMessage);
                } else {
                  // Update existing message content for streaming
                  currentAgentMessage.content = eventData.content.data;
                  currentAgentMessage.partial = eventData.content.partial ?? true;
                  // Force immediate update by creating a new object reference
                  updateLastMessage({ ...currentAgentMessage });
                }
              } else {
                // Handle other event types
                await handleStreamEvent(eventData, currentAgentMessage);
              }
            } catch (e) {
              console.error('Error parsing SSE data:', e);
            }
          }
        }
      }

      setStreamingStatus('idle');
    } catch (error) {
      console.error('Chat stream error:', error);
      setConnectionStatus('disconnected');
      setStreamingStatus('idle');
      
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'error',
        content: 'Failed to send message. Please try again.',
        timestamp: new Date().toISOString(),
        author: 'system'
      };
      addMessage(errorMessage);
    }
  }, [sessionToken, addMessage, setConnectionStatus, updateLastMessage, setStreamingStatus, getCurrentSession, createSession, currentSessionId]);

  return {
    sendMessage,
  };
}