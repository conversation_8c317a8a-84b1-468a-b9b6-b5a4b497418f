# Career Coach Central

This is the main repository for Career Coach Central, a full-stack AI-driven platform designed to empower career coaches. It consists of a back-end service built with Python and FastAPI, and a front-end application built with Next.js and TypeScript.

## Overview

Career Coach Central provides a comprehensive suite of tools for career coaches, including a conversational interface to manage clients, gain job market insights, and generate career development reports. The project is divided into two main components:

*   **`ccc-back-end`**: A robust back-end service that handles business logic, data management, and AI agent orchestration.
*   **`ccc-front-end`**: A modern, responsive front-end application that provides the user interface for interacting with the platform.

---

## 🚀 Back-end (Python/FastAPI)

The back-end is an AI-driven service that provides a conversational interface for career coaches to manage clients, get job market insights, and generate career development reports.

### Technologies

*   **Framework**: FastAPI
*   **AI Agents**: Google Agent Development Kit (ADK)
*   **Databases**: PostgreSQL, Azure Cosmos DB
*   **Authentication**: Firebase Authentication
*   **Package Management**: `uv`

### Setup and Run

1.  **Navigate to the back-end directory:**
    ```bash
    cd ccc-back-end
    ```

2.  **Install dependencies:**
    ```bash
    pip install -e .
    ```

3.  **Configure environment variables:**
    Copy `.env.example` to `.env` and fill in the required values for your databases and authentication services.

4.  **Set up the database:**
    ```bash
    createdb career_coach_db
    psql -d career_coach_db -f sql/schema.sql
    psql -d career_coach_db -f sql/initial_data.sql
    ```

5.  **Run the development server:**
    ```bash
    uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
    ```

For more detailed instructions, including Docker deployment, see the [back-end README](ccc-back-end/README.md).

---

## 🎨 Front-end (Next.js/TypeScript)

The front-end is a Next.js application that provides a modern and intuitive user interface for career coaches to interact with the Career Coach Central platform.

### Technologies

*   **Framework**: Next.js
*   **Language**: TypeScript
*   **Package Management**: `pnpm`
*   **UI**: React

### Setup and Run

1.  **Navigate to the front-end directory:**
    ```bash
    cd ccc-front-end
    ```

2.  **Install dependencies:**
    ```bash
    pnpm install
    ```

3.  **Run the development server:**
    ```bash
    pnpm dev
    ```

The application will be available at `http://localhost:3000`.

For more details, see the [front-end README](ccc-front-end/README.md).